#!/usr/bin/env python3
"""
创建测试PAK配置的脚本
"""

import os
import json

def create_test_config():
    """创建一个简单的测试配置文件"""
    
    # 测试配置数据
    config_data = {
        "ConfigName": "TestMap1_Config",
        "OutputPakFileName": "TestMap1.pak",
        "OutputDirectory": "Paks/TestMaps",
        "Map": "/Game/Maps/TestMap1/NewMap",
        "Description": "Test configuration for TestMap1 PAK packaging"
    }
    
    # 保存配置到JSON文件（用于参考）
    config_file = "test_pak_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print(f"Created test configuration file: {config_file}")
    print("Configuration data:")
    print(json.dumps(config_data, indent=2, ensure_ascii=False))
    
    # 创建输出目录
    output_dir = os.path.join("Paks", "TestMaps")
    os.makedirs(output_dir, exist_ok=True)
    print(f"Created output directory: {output_dir}")
    
    return config_data

def create_blueprint_test_script():
    """创建Blueprint测试脚本的伪代码"""
    
    blueprint_code = """
// Blueprint伪代码 - 测试PAK打包功能
// 这个代码需要在UE5编辑器中的Blueprint中实现

Event BeginPlay
{
    // 获取PAK管理器实例
    PakManager = Get Neo Pak Manager Instance
    
    if (PakManager == null)
    {
        Print String "Failed to get PAK Manager instance"
        return
    }
    
    // 创建测试资产路径数组
    AssetPaths = [
        "/Game/Maps/TestMap1/NewMap",
        "/Game/Maps/TestMap1/NewDataAsset", 
        "/Game/Maps/TestMap1/NewBlueprint",
        "/Game/Maps/TestMap1/NewBlueprint1"
    ]
    
    // 设置输出路径
    OutputPath = "E:/UnrealProjects/NeoLive/Paks/TestMaps/TestMap1.pak"
    
    Print String "Starting PAK creation test..."
    Print String ("Output path: " + OutputPath)
    
    // 调用PAK创建函数
    bool Success = PakManager.CreatePakFile(AssetPaths, OutputPath)
    
    if (Success)
    {
        Print String "✓ PAK file created successfully!"
        Print String ("PAK file location: " + OutputPath)
    }
    else
    {
        Print String "✗ PAK file creation failed!"
    }
}

// 或者使用配置资产的方式
Event TestWithConfig
{
    // 创建地图PAK配置
    MapConfig = Create Object (Neo Map Pak Config)
    MapConfig.ConfigName = "TestMap1_Config"
    MapConfig.OutputPakFileName = "TestMap1.pak"
    MapConfig.OutputDirectory = "Paks/TestMaps"
    MapConfig.Map = Load Object "/Game/Maps/TestMap1/NewMap"
    
    // 获取PAK管理器并执行打包
    PakManager = Get Neo Pak Manager Instance
    bool Success = PakManager.PackageFromConfig(MapConfig)
    
    if (Success)
    {
        Print String "✓ Config-based PAK creation successful!"
    }
    else
    {
        Print String "✗ Config-based PAK creation failed!"
    }
}
"""
    
    with open("blueprint_test_script.txt", 'w', encoding='utf-8') as f:
        f.write(blueprint_code)
    
    print("Created Blueprint test script: blueprint_test_script.txt")

def create_cpp_test_code():
    """创建C++测试代码"""
    
    cpp_code = """
// C++测试代码 - 可以添加到游戏模块中进行测试

#include "PakManager/NeoPakManager.h"
#include "Config/NeoMapPakConfig.h"

void TestPakCreation()
{
    // 获取PAK管理器实例
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get PAK Manager instance"));
        return;
    }
    
    // 测试资产路径
    TArray<FString> AssetPaths = {
        TEXT("/Game/Maps/TestMap1/NewMap"),
        TEXT("/Game/Maps/TestMap1/NewDataAsset"),
        TEXT("/Game/Maps/TestMap1/NewBlueprint"),
        TEXT("/Game/Maps/TestMap1/NewBlueprint1")
    };
    
    // 输出路径
    FString OutputPath = TEXT("E:/UnrealProjects/NeoLive/Paks/TestMaps/TestMap1.pak");
    
    UE_LOG(LogTemp, Log, TEXT("Starting PAK creation test..."));
    UE_LOG(LogTemp, Log, TEXT("Output path: %s"), *OutputPath);
    
    // 创建PAK文件
    bool bSuccess = PakManager->CreatePakFile(AssetPaths, OutputPath);
    
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("✓ PAK file created successfully!"));
        UE_LOG(LogTemp, Log, TEXT("PAK file location: %s"), *OutputPath);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("✗ PAK file creation failed!"));
    }
}

void TestPakCreationWithConfig()
{
    // 创建地图PAK配置
    UNeoMapPakConfig* MapConfig = NewObject<UNeoMapPakConfig>();
    MapConfig->ConfigName = TEXT("TestMap1_Config");
    MapConfig->OutputPakFileName = TEXT("TestMap1.pak");
    MapConfig->OutputDirectory.Path = TEXT("Paks/TestMaps");
    
    // 设置地图资产
    MapConfig->Map = TSoftObjectPtr<UWorld>(FSoftObjectPath(TEXT("/Game/Maps/TestMap1/NewMap")));
    
    // 获取PAK管理器并执行打包
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (PakManager)
    {
        bool bSuccess = PakManager->PackageFromConfig(MapConfig);
        
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("✓ Config-based PAK creation successful!"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("✗ Config-based PAK creation failed!"));
        }
    }
}
"""
    
    with open("cpp_test_code.cpp", 'w', encoding='utf-8') as f:
        f.write(cpp_code)
    
    print("Created C++ test code: cpp_test_code.cpp")

def main():
    """主函数"""
    print("=== 创建PAK打包测试配置 ===")
    
    # 创建测试配置
    config_data = create_test_config()
    
    # 创建测试脚本
    create_blueprint_test_script()
    create_cpp_test_code()
    
    print("\n=== 测试说明 ===")
    print("1. 已创建测试配置文件和输出目录")
    print("2. 已生成Blueprint和C++测试代码")
    print("3. 可以在UE5编辑器中使用这些代码来测试PAK打包功能")
    print("4. 修复后的代码应该能够正确找到所有文件并创建PAK")
    
    print("\n=== 下一步操作 ===")
    print("1. 在UE5编辑器中打开项目")
    print("2. 创建一个测试Blueprint或在C++代码中添加测试函数")
    print("3. 运行测试代码验证PAK打包是否正常工作")
    print("4. 检查生成的PAK文件是否包含正确的资产")

if __name__ == "__main__":
    main()
