// Copyright Epic Games, Inc. All Rights Reserved.

#include "CustomCookTool.h"
#include "Engine/Engine.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "UObject/SavePackage.h"
#include "Serialization/ArchiveCookContext.h"
#include "Serialization/ArchiveCookData.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/PackageName.h"
#include "UObject/LinkerLoad.h"
#include "UObject/UObjectGlobals.h"

UCustomCookTool::UCustomCookTool()
{
}

FCustomCookResult UCustomCookTool::CookDirectory(const FString& DirectoryPath, const FCustomCookSettings& Settings)
{
    FCustomCookResult Result;
    
    // 验证设置
    FString ValidationError;
    if (!ValidateCookSettings(Settings, ValidationError))
    {
        Result.ErrorMessage = ValidationError;
        return Result;
    }

    // 获取目录下的所有资产
    TArray<FString> AssetPaths = GetAssetsInDirectory(DirectoryPath, true);
    if (AssetPaths.Num() == 0)
    {
        Result.ErrorMessage = FString::Printf(TEXT("No assets found in directory: %s"), *DirectoryPath);
        return Result;
    }

    UE_LOG(LogTemp, Log, TEXT("Found %d assets in directory: %s"), AssetPaths.Num(), *DirectoryPath);

    // Cook 所有资产
    return CookAssets(AssetPaths, Settings);
}

FCustomCookResult UCustomCookTool::CookAssets(const TArray<FString>& AssetPaths, const FCustomCookSettings& Settings)
{
    FCustomCookResult Result;
    
    // 初始化 Cook 环境
    const ITargetPlatform* TargetPlatform = nullptr;
    if (!InitializeCookEnvironment(Settings, TargetPlatform))
    {
        Result.ErrorMessage = TEXT("Failed to initialize cook environment");
        return Result;
    }

    // 创建输出目录
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.CreateDirectoryTree(*Settings.OutputDirectory))
    {
        Result.ErrorMessage = FString::Printf(TEXT("Failed to create output directory: %s"), *Settings.OutputDirectory);
        return Result;
    }

    // Cook 每个资产
    TSet<UPackage*> ProcessedPackages;
    
    for (const FString& AssetPath : AssetPaths)
    {
        if (ShouldExcludePackage(AssetPath, Settings))
        {
            continue;
        }

        // 加载包
        UPackage* Package = LoadPackage(nullptr, *AssetPath, LOAD_None);
        if (!Package)
        {
            Result.FailedPackages.Add(AssetPath);
            UE_LOG(LogTemp, Warning, TEXT("Failed to load package: %s"), *AssetPath);
            continue;
        }

        // 避免重复处理
        if (ProcessedPackages.Contains(Package))
        {
            continue;
        }
        ProcessedPackages.Add(Package);

        // Cook 包
        FString CookError;
        if (CookSinglePackage(Package, TargetPlatform, Settings, CookError))
        {
            Result.CookedPackages.Add(AssetPath);
            Result.CookedPackageCount++;
            
            // 处理依赖
            if (Settings.bIncludeDependencies)
            {
                TArray<UPackage*> Dependencies;
                CollectDependencies(Package, Dependencies);
                
                for (UPackage* DepPackage : Dependencies)
                {
                    if (!ProcessedPackages.Contains(DepPackage))
                    {
                        ProcessedPackages.Add(DepPackage);
                        FString DepError;
                        if (CookSinglePackage(DepPackage, TargetPlatform, Settings, DepError))
                        {
                            Result.CookedPackages.Add(DepPackage->GetName());
                            Result.CookedPackageCount++;
                        }
                    }
                }
            }
        }
        else
        {
            Result.FailedPackages.Add(AssetPath);
            UE_LOG(LogTemp, Error, TEXT("Failed to cook package %s: %s"), *AssetPath, *CookError);
        }
    }

    Result.bSuccess = Result.FailedPackages.Num() == 0;
    
    UE_LOG(LogTemp, Log, TEXT("Cook completed. Success: %d, Failed: %d"), 
           Result.CookedPackageCount, Result.FailedPackages.Num());

    return Result;
}

FCustomCookResult UCustomCookTool::CookPackage(UPackage* Package, const FCustomCookSettings& Settings)
{
    FCustomCookResult Result;
    
    if (!Package)
    {
        Result.ErrorMessage = TEXT("Invalid package");
        return Result;
    }

    const ITargetPlatform* TargetPlatform = nullptr;
    if (!InitializeCookEnvironment(Settings, TargetPlatform))
    {
        Result.ErrorMessage = TEXT("Failed to initialize cook environment");
        return Result;
    }

    FString CookError;
    if (CookSinglePackage(Package, TargetPlatform, Settings, CookError))
    {
        Result.bSuccess = true;
        Result.CookedPackageCount = 1;
        Result.CookedPackages.Add(Package->GetName());
    }
    else
    {
        Result.ErrorMessage = CookError;
        Result.FailedPackages.Add(Package->GetName());
    }

    return Result;
}

bool UCustomCookTool::InitializeCookEnvironment(const FCustomCookSettings& Settings, const ITargetPlatform*& OutTargetPlatform)
{
    // 获取目标平台管理器
    ITargetPlatformManagerModule* TPM = GetTargetPlatformManager();
    if (!TPM)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get TargetPlatformManager"));
        return false;
    }

    // 查找目标平台
    OutTargetPlatform = TPM->FindTargetPlatform(*Settings.TargetPlatform);
    if (!OutTargetPlatform)
    {
        UE_LOG(LogTemp, Error, TEXT("Target platform not found: %s"), *Settings.TargetPlatform);
        return false;
    }

    return true;
}

bool UCustomCookTool::CookSinglePackage(UPackage* Package, const ITargetPlatform* TargetPlatform, 
                                       const FCustomCookSettings& Settings, FString& OutError)
{
    if (!Package || !TargetPlatform)
    {
        OutError = TEXT("Invalid package or target platform");
        return false;
    }

    // 检查是否是瞬态包
    if (Package == GetTransientPackage())
    {
        OutError = TEXT("Cannot cook transient package");
        return false;
    }

    try
    {
        // 创建 Cook 上下文
        UE::Cook::ICookInfo* CookInfo = nullptr;
        FArchiveCookContext CookContext(Package, 
                                       UE::Cook::ECookType::ByTheBook,
                                       UE::Cook::ECookingDLC::No,
                                       TargetPlatform, 
                                       CookInfo);

        // 创建 Cook 数据
        FArchiveCookData CookData(*TargetPlatform, CookContext);

        // 获取输出路径
        FString OutputPath = GetCookedPackagePath(Package->GetName(), Settings);
        
        // 保存 Cook 后的包
        return SaveCookedPackage(Package, TargetPlatform, OutputPath, Settings);
    }
    catch (const std::exception& e)
    {
        OutError = FString::Printf(TEXT("Exception during cooking: %s"), ANSI_TO_TCHAR(e.what()));
        return false;
    }
}

bool UCustomCookTool::SaveCookedPackage(UPackage* Package, const ITargetPlatform* TargetPlatform, 
                                        const FString& OutputPath, const FCustomCookSettings& Settings)
{
    // 配置保存参数
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.SaveFlags = SAVE_NoError | SAVE_Async;
    
    if (Settings.bUnversioned)
    {
        SaveArgs.SaveFlags |= SAVE_Unversioned;
    }
    
    if (Settings.bCompressed)
    {
        SaveArgs.SaveFlags |= SAVE_CompressPackage;
    }

    // 保存包
    UPackage::SavePackage(Package, nullptr, *OutputPath, SaveArgs);
    
    UE_LOG(LogTemp, Log, TEXT("Cooked package saved: %s -> %s"), *Package->GetName(), *OutputPath);
    return true;
}

void UCustomCookTool::CollectDependencies(UPackage* Package, TArray<UPackage*>& OutDependencies)
{
    if (!Package)
    {
        return;
    }

    // 获取包中的所有对象
    TArray<UObject*> Objects;
    GetObjectsWithOuter(Package, Objects, true);

    TSet<UPackage*> FoundDependencies;

    for (UObject* Object : Objects)
    {
        if (!Object)
        {
            continue;
        }

        // 收集对象引用的其他包
        TArray<UObject*> ReferencedObjects;
        FReferenceFinder ObjectReferenceCollector(ReferencedObjects, Object, false, true, true, true);
        ObjectReferenceCollector.FindReferences(Object);

        for (UObject* ReferencedObject : ReferencedObjects)
        {
            if (ReferencedObject && ReferencedObject->GetPackage() != Package)
            {
                UPackage* ReferencedPackage = ReferencedObject->GetPackage();
                if (ReferencedPackage && ReferencedPackage != GetTransientPackage())
                {
                    FoundDependencies.Add(ReferencedPackage);
                }
            }
        }
    }

    OutDependencies = FoundDependencies.Array();
}

FString UCustomCookTool::GetCookedPackagePath(const FString& PackageName, const FCustomCookSettings& Settings)
{
    FString CleanPackageName = PackageName;
    if (CleanPackageName.StartsWith(TEXT("/Game/")))
    {
        CleanPackageName = CleanPackageName.RightChop(6); // 移除 "/Game/"
    }

    return FPaths::Combine(Settings.OutputDirectory, Settings.TargetPlatform, CleanPackageName + TEXT(".uasset"));
}

bool UCustomCookTool::ShouldExcludePackage(const FString& PackagePath, const FCustomCookSettings& Settings)
{
    for (const FString& Pattern : Settings.ExcludePatterns)
    {
        if (PackagePath.Contains(Pattern))
        {
            return true;
        }
    }
    return false;
}

TArray<FString> UCustomCookTool::GetAssetsInDirectory(const FString& DirectoryPath, bool bRecursive)
{
    TArray<FString> AssetPaths;
    
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // 确保资产注册表已扫描
    AssetRegistry.SearchAllAssets(true);

    // 获取目录下的资产
    TArray<FAssetData> AssetDataList;
    FString SearchPath = DirectoryPath;
    if (!SearchPath.StartsWith(TEXT("/Game/")))
    {
        SearchPath = TEXT("/Game/") + SearchPath;
    }

    AssetRegistry.GetAssetsByPath(*SearchPath, AssetDataList, bRecursive);

    for (const FAssetData& AssetData : AssetDataList)
    {
        AssetPaths.Add(AssetData.PackageName.ToString());
    }

    return AssetPaths;
}

bool UCustomCookTool::ValidateCookSettings(const FCustomCookSettings& Settings, FString& OutErrorMessage)
{
    if (Settings.OutputDirectory.IsEmpty())
    {
        OutErrorMessage = TEXT("Output directory cannot be empty");
        return false;
    }

    if (Settings.TargetPlatform.IsEmpty())
    {
        OutErrorMessage = TEXT("Target platform cannot be empty");
        return false;
    }

    // 检查目标平台是否有效
    ITargetPlatformManagerModule* TPM = GetTargetPlatformManager();
    if (!TPM)
    {
        OutErrorMessage = TEXT("TargetPlatformManager not available");
        return false;
    }

    const ITargetPlatform* TargetPlatform = TPM->FindTargetPlatform(*Settings.TargetPlatform);
    if (!TargetPlatform)
    {
        OutErrorMessage = FString::Printf(TEXT("Invalid target platform: %s"), *Settings.TargetPlatform);
        return false;
    }

    return true;
}