// Copyright Epic Games, Inc. All Rights Reserved.

#include "CustomCookToolBlueprintLibrary.h"
#include "Interfaces/ITargetPlatformManagerModule.h"
#include "AssetRegistry/AssetRegistryModule.h"

FCustomCookResult UCustomCookToolBlueprintLibrary::QuickCookDirectory(const FString& DirectoryPath, 
                                                                     const FString& TargetPlatform,
                                                                     const FString& OutputDirectory)
{
    FCustomCookSettings Settings;
    Settings.TargetPlatform = TargetPlatform;
    
    if (!OutputDirectory.IsEmpty())
    {
        Settings.OutputDirectory = OutputDirectory;
    }

    return UCustomCookTool::CookDirectory(DirectoryPath, Settings);
}

FCustomCookResult UCustomCookToolBlueprintLibrary::QuickCookAssets(const TArray<FString>& AssetPaths,
                                                                  const FString& TargetPlatform,
                                                                  const FString& OutputDirectory)
{
    FCustomCookSettings Settings;
    Settings.TargetPlatform = TargetPlatform;
    
    if (!OutputDirectory.IsEmpty())
    {
        Settings.OutputDirectory = OutputDirectory;
    }

    return UCustomCookTool::CookAssets(AssetPaths, Settings);
}

TArray<FString> UCustomCookToolBlueprintLibrary::GetAvailableTargetPlatforms()
{
    TArray<FString> PlatformNames;
    
    ITargetPlatformManagerModule* TPM = GetTargetPlatformManager();
    if (TPM)
    {
        const TArray<ITargetPlatform*>& Platforms = TPM->GetTargetPlatforms();
        for (const ITargetPlatform* Platform : Platforms)
        {
            if (Platform)
            {
                PlatformNames.Add(Platform->PlatformName());
            }
        }
    }
    
    return PlatformNames;
}

TArray<FString> UCustomCookToolBlueprintLibrary::GetProjectDirectories()
{
    TArray<FString> Directories;
    
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    TArray<FString> PathList;
    AssetRegistry.GetAllPackagePaths(PathList);

    TSet<FString> UniqueDirectories;
    for (const FString& PackagePath : PathList)
    {
        if (PackagePath.StartsWith(TEXT("/Game/")))
        {
            FString Directory = FPaths::GetPath(PackagePath);
            UniqueDirectories.Add(Directory);
        }
    }

    Directories = UniqueDirectories.Array();
    Directories.Sort();
    
    return Directories;
}