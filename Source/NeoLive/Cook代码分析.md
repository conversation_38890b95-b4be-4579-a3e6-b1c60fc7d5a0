# Unreal Engine Cook 系统代码分析

## 概述
本文档分析了 `/Source/Runtime/CoreUObject/Public/Cooker` 目录下的 Cook 相关代码，主要包含多进程 Cook 系统的核心组件。

## 核心文件分析

### 1. MPCollector.h - 多进程收集器
这是多进程 Cook 系统的消息收集和通信核心：

#### 主要类：
- **IMPCollector**: 抽象接口，用于在远程 CookWorker 上收集数据并发送到 Director 进行聚合
- **FMPCollectorClientTickContext**: 客户端 Tick 上下文，处理平台相关的消息
- **FMPCollectorServerTickContext**: 服务端 Tick 上下文，处理工作进程启动等事件
- **FMPCollectorClientTickPackageContext**: 客户端包 Tick 上下文，处理单个包的 Cook 结果
- **FMPCollectorServerTickPackageContext**: 服务端包 Tick 上下文
- **FMPCollectorClientMessageContext**: 客户端消息上下文
- **FMPCollectorServerMessageContext**: 服务端消息上下文，包含工作进程信息

#### 关键功能：
- 平台到整数的转换：`PlatformToInt()` / `IntToPlatform()`
- 消息添加：`AddMessage()`, `AddAsyncMessage()`, `AddPlatformMessage()`
- 工作进程管理：WorkerId, ProfileId 跟踪

### 2. CookDependency.h - Cook 依赖管理
管理 Cook 过程中的依赖关系：

#### 主要功能：
- **文件依赖**: `FCookDependency::File()` - 依赖文件内容变化
- **原生类依赖**: `FCookDependency::NativeClass()` - 依赖类架构变化
- **重定向依赖**: `FCookDependency::RedirectionTarget()` - 依赖对象重定向
- **资产注册表查询依赖**: `FCookDependency::AssetRegistryQuery()` - 依赖资产查询结果

#### BuildResult 类型：
- **NAME_Save**: Cook 后的包字节数据
- **NAME_Load**: 编辑器中加载的包字节数据（默认传递依赖）

## Cook 文件夹的方法

### 1. 使用 UnrealEditor-Cmd 命令行
```bash
UnrealEditor-Cmd.exe "项目路径.uproject" -run=Cook -TargetPlatform=Windows -CookDir=/Game/目标文件夹 -unversioned -stdout -unattended
```

### 2. 参数说明：
- `-run=Cook`: 运行 Cook 命令
- `-TargetPlatform=Windows`: 目标平台
- `-CookDir=/Game/目标文件夹`: 指定要 Cook 的目录
- `-unversioned`: 不使用版本控制
- `-stdout`: 输出到标准输出
- `-unattended`: 无人值守模式

### 3. 示例用法：
```bash
# Cook Maps 文件夹
UnrealEditor-Cmd.exe "E:\Project\MyGame.uproject" -run=Cook -TargetPlatform=Windows -CookDir=/Game/Maps -unversioned -stdout -unattended

# Cook 特定地图
UnrealEditor-Cmd.exe "E:\Project\MyGame.uproject" -run=Cook -TargetPlatform=Windows -Map=MyMap -unversioned -stdout -unattended
```

## 多进程 Cook 架构

### 工作流程：
1. **CookDirector** 启动多个 **CookWorker** 进程
2. **MPCollector** 收集各 Worker 的 Cook 数据
3. 通过 **FCbObject** 进行进程间通信
4. 最终聚合所有结果到输出目录

### 关键组件交互：
- Director ↔ Worker: 通过 TCP 连接通信
- 消息序列化: 使用 CompactBinary 格式
- 负载均衡: 支持 Striped 和 CookBurden 算法

## 输出位置
Cook 后的文件通常保存在：
```
项目目录/Saved/Cooked/平台名称/项目名称/Content/
```

## 注意事项
1. 确保项目文件路径正确
2. 目标平台必须已安装和配置
3. Cook 过程可能需要较长时间，取决于内容大小
4. 多进程 Cook 可以显著提升大型项目的 Cook 速度