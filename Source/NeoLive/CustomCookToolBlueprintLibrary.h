// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "CustomCookTool.h"
#include "CustomCookToolBlueprintLibrary.generated.h"

UCLASS()
class NEOLIVE_API UCustomCookToolBlueprintLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // 便捷的蓝图接口
    UFUNCTION(BlueprintCallable, Category = "Custom Cook", meta = (CallInEditor = "true"))
    static FCustomCookResult QuickCookDirectory(const FString& DirectoryPath, 
                                               const FString& TargetPlatform = TEXT("Windows"),
                                               const FString& OutputDirectory = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Custom Cook", meta = (CallInEditor = "true"))
    static FCustomCookResult QuickCookAssets(const TArray<FString>& AssetPaths,
                                            const FString& TargetPlatform = TEXT("Windows"),
                                            const FString& OutputDirectory = TEXT(""));

    // 获取可用的目标平台
    UFUNCTION(BlueprintCallable, Category = "Custom Cook")
    static TArray<FString> GetAvailableTargetPlatforms();

    // 获取项目中的所有目录
    UFUNCTION(BlueprintCallable, Category = "Custom Cook")
    static TArray<FString> GetProjectDirectories();
};