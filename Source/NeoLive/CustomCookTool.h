// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/World.h"
#include "Serialization/ArchiveCookContext.h"
#include "Serialization/ArchiveCookData.h"
#include "HAL/PlatformFilemanager.h"
#include "Interfaces/ITargetPlatform.h"
#include "Interfaces/ITargetPlatformManagerModule.h"
#include "UObject/SavePackage.h"
#include "UObject/Package.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "CustomCookTool.generated.h"

USTRUCT(BlueprintType)
struct FCustomCookSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString OutputDirectory;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString TargetPlatform = TEXT("Windows");

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUnversioned = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bCompressed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIncludeDependencies = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> ExcludePatterns;

    FCustomCookSettings()
    {
        OutputDirectory = FPaths::ProjectSavedDir() / TEXT("CustomCooked");
    }
};

USTRUCT(BlueprintType)
struct FCustomCookResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly)
    int32 CookedPackageCount = 0;

    UPROPERTY(BlueprintReadOnly)
    TArray<FString> CookedPackages;

    UPROPERTY(BlueprintReadOnly)
    TArray<FString> FailedPackages;

    UPROPERTY(BlueprintReadOnly)
    FString ErrorMessage;
};

UCLASS(BlueprintType)
class NEOLIVE_API UCustomCookTool : public UObject
{
    GENERATED_BODY()

public:
    UCustomCookTool();

    // 主要 Cook 接口
    UFUNCTION(BlueprintCallable, Category = "Custom Cook")
    static FCustomCookResult CookDirectory(const FString& DirectoryPath, const FCustomCookSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Custom Cook")
    static FCustomCookResult CookAssets(const TArray<FString>& AssetPaths, const FCustomCookSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Custom Cook")
    static FCustomCookResult CookPackage(UPackage* Package, const FCustomCookSettings& Settings);

    // 辅助功能
    UFUNCTION(BlueprintCallable, Category = "Custom Cook")
    static TArray<FString> GetAssetsInDirectory(const FString& DirectoryPath, bool bRecursive = true);

    UFUNCTION(BlueprintCallable, Category = "Custom Cook")
    static bool ValidateCookSettings(const FCustomCookSettings& Settings, FString& OutErrorMessage);

private:
    // 内部实现
    static bool InitializeCookEnvironment(const FCustomCookSettings& Settings, const ITargetPlatform*& OutTargetPlatform);
    static bool CookSinglePackage(UPackage* Package, const ITargetPlatform* TargetPlatform, const FCustomCookSettings& Settings, FString& OutError);
    static bool SaveCookedPackage(UPackage* Package, const ITargetPlatform* TargetPlatform, const FString& OutputPath, const FCustomCookSettings& Settings);
    static void CollectDependencies(UPackage* Package, TArray<UPackage*>& OutDependencies);
    static FString GetCookedPackagePath(const FString& PackageName, const FCustomCookSettings& Settings);
    static bool ShouldExcludePackage(const FString& PackagePath, const FCustomCookSettings& Settings);
};