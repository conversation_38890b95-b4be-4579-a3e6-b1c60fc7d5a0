#!/usr/bin/env python3
"""
测试脚本：验证PAK打包路径转换逻辑
"""

import os
import sys

def test_path_conversion():
    """测试路径转换逻辑"""
    
    # 模拟的项目路径
    project_root = r"E:\UnrealProjects\NeoLive"
    content_dir = os.path.join(project_root, "Content")
    
    # 测试用例：资产路径 -> 期望的文件路径
    test_cases = [
        ("/Game/Maps/TestMap1/NewMap", "Content/Maps/TestMap1/NewMap.umap"),
        ("/Game/Maps/TestMap1/NewDataAsset", "Content/Maps/TestMap1/NewDataAsset.uasset"),
        ("/Game/Maps/TestMap1/NewBlueprint", "Content/Maps/TestMap1/NewBlueprint.uasset"),
        ("/Game/Maps/TestMap1/NewBlueprint1", "Content/Maps/TestMap1/NewBlueprint1.uasset"),
        ("/Game/Maps/TestMap1/NewMap.NewMap", "Content/Maps/TestMap1/NewMap.umap"),  # 重复名称情况
    ]
    
    print("=== PAK路径转换测试 ===")
    print(f"项目根目录: {project_root}")
    print(f"Content目录: {content_dir}")
    print()
    
    for asset_path, expected_relative in test_cases:
        print(f"资产路径: {asset_path}")
        
        # 模拟转换逻辑
        relative_path = asset_path
        if relative_path.startswith("/Game/"):
            relative_path = relative_path[6:]  # 移除"/Game/"
        
        # 处理重复名称情况（如NewMap.NewMap）
        if "." in relative_path:
            parts = relative_path.split(".")
            if len(parts) == 2 and parts[0].endswith("/" + parts[1]):
                # 这是重复名称情况，使用基础路径
                relative_path = parts[0]
        
        # 构建完整路径
        full_path = os.path.join(content_dir, relative_path.replace("/", os.sep))
        
        # 尝试不同扩展名
        extensions = [".umap", ".uasset", ".ubulk"]
        found_file = None
        
        for ext in extensions:
            test_path = full_path + ext
            if os.path.exists(test_path):
                found_file = test_path
                break
        
        expected_full = os.path.join(project_root, expected_relative.replace("/", os.sep))
        
        print(f"  转换后相对路径: {relative_path}")
        print(f"  期望文件路径: {expected_full}")
        print(f"  实际找到文件: {found_file}")
        print(f"  文件存在: {found_file is not None and os.path.exists(found_file)}")
        
        if found_file:
            print(f"  ✓ 成功找到文件")
        else:
            print(f"  ✗ 未找到文件")
            # 列出目录内容以便调试
            dir_path = os.path.dirname(full_path)
            if os.path.exists(dir_path):
                files = os.listdir(dir_path)
                print(f"  目录 {dir_path} 中的文件: {files}")
        
        print()

def check_actual_files():
    """检查实际文件结构"""
    project_root = r"E:\UnrealProjects\NeoLive"
    test_map_dir = os.path.join(project_root, "Content", "Maps", "TestMap1")
    
    print("=== 实际文件检查 ===")
    print(f"检查目录: {test_map_dir}")
    
    if os.path.exists(test_map_dir):
        files = os.listdir(test_map_dir)
        print(f"目录中的文件:")
        for file in sorted(files):
            file_path = os.path.join(test_map_dir, file)
            size = os.path.getsize(file_path)
            print(f"  {file} ({size} bytes)")
    else:
        print("目录不存在!")
    
    print()

if __name__ == "__main__":
    check_actual_files()
    test_path_conversion()
