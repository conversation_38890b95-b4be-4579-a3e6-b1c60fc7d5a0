#!/usr/bin/env python3
"""
测试脚本：验证PAK打包修复是否有效
模拟C++代码中的路径转换逻辑
"""

import os
import sys

def simulate_convert_asset_paths_to_file_paths(asset_paths, content_dir):
    """
    模拟C++中的ConvertAssetPathsToFilePaths函数
    """
    file_paths = []
    
    print(f"Converting asset paths to file paths. Content directory: {content_dir}")
    
    for asset_path in asset_paths:
        print(f"Converting asset path: {asset_path}")
        
        # 首先尝试标准转换（模拟FPackageName::TryConvertLongPackageNameToFilename）
        file_path = None
        conversion_successful = False
        
        # 模拟替代转换方法
        alternative_file_path = convert_asset_path_alternative(asset_path, content_dir)
        if alternative_file_path:
            print(f"  Alternative conversion successful: {alternative_file_path}")
            
            # 验证文件是否真的存在
            if os.path.exists(alternative_file_path):
                print(f"  File exists, using alternative path")
                file_paths.append(alternative_file_path)
                conversion_successful = True
            else:
                print(f"  Alternative path does not exist: {alternative_file_path}")
        
        if not conversion_successful:
            # 回退到标准转换（简化版）
            file_path = simulate_standard_conversion(asset_path, content_dir)
            if file_path:
                print(f"  Standard conversion result: {file_path}")
                
                # 验证标准转换的文件是否存在
                if os.path.exists(file_path):
                    print(f"  Standard conversion file exists")
                    file_paths.append(file_path)
                    conversion_successful = True
                else:
                    print(f"  Standard conversion file does not exist: {file_path}")
            else:
                print(f"  Standard conversion failed for: {asset_path}")
        
        if not conversion_successful:
            print(f"  Failed to convert asset path to file path: {asset_path}")
            file_paths.append("")
    
    return file_paths

def convert_asset_path_alternative(asset_path, content_dir):
    """
    模拟C++中的ConvertAssetPathAlternative函数
    """
    print(f"Alternative conversion for: {asset_path}")
    print(f"Content directory: {content_dir}")
    
    # 确保Content目录路径是绝对路径且格式正确
    content_dir = os.path.abspath(content_dir)
    content_dir = os.path.normpath(content_dir)
    
    # Remove /Game/ prefix
    relative_path = asset_path
    if relative_path.startswith("/Game/"):
        relative_path = relative_path[6:]  # Remove "/Game/"
        print(f"Removed /Game/ prefix, relative path: {relative_path}")
    
    # Handle special case for assets with duplicate names (like Maps/NewMap.NewMap)
    if "." in relative_path:
        parts = relative_path.split(".")
        if len(parts) == 2:
            base_name = parts[0]
            extension = parts[1]
            
            # Check if the extension matches the base name (like NewMap.NewMap)
            base_file_name = os.path.basename(base_name)
            if extension == base_file_name:
                # This is likely a map or similar asset, use just the base path
                relative_path = base_name
                print(f"Detected duplicate name format, using base path: {relative_path}")
    
    # Try different file extensions
    possible_extensions = [".umap", ".uasset", ".ubulk"]
    
    for extension in possible_extensions:
        # 使用正确的路径组合方式，避免相对路径问题
        test_path = os.path.join(content_dir, relative_path.replace("/", os.sep) + extension)
        # 规范化路径，移除任何相对路径符号
        test_path = os.path.normpath(test_path)
        
        print(f"Testing path: {test_path}")
        
        if os.path.exists(test_path):
            print(f"Found alternative file path: {test_path}")
            return test_path
    
    print(f"No alternative file path found for: {asset_path}")
    return None

def simulate_standard_conversion(asset_path, content_dir):
    """
    模拟标准的包名转换
    """
    if not asset_path.startswith("/Game/"):
        return None
    
    # 移除/Game/前缀
    relative_path = asset_path[6:]
    
    # 构建文件路径
    file_path = os.path.join(content_dir, relative_path.replace("/", os.sep))
    
    # 尝试不同扩展名
    for ext in [".umap", ".uasset", ".ubulk"]:
        test_path = file_path + ext
        if os.path.exists(test_path):
            return test_path
    
    return None

def simulate_create_response_file(asset_paths, file_paths):
    """
    模拟创建响应文件的过程
    """
    response_lines = []
    
    print("Creating response file...")
    
    for i, (asset_path, file_path) in enumerate(zip(asset_paths, file_paths)):
        print(f"Processing asset {i}: {asset_path} -> {file_path}")
        
        if file_path and file_path != "":
            # 检查源文件是否存在
            if os.path.exists(file_path):
                # 将资产路径转换为PAK内的路径格式
                pak_internal_path = asset_path
                if pak_internal_path.startswith("/Game/"):
                    pak_internal_path = pak_internal_path[6:]  # 移除"/Game/"前缀
                
                # Format: "SourceFile" "DestinationPath"
                response_line = f'"{file_path}" "{pak_internal_path}"'
                response_lines.append(response_line)
                print(f"Added to response file: {response_line}")
            else:
                print(f"Source file does not exist: {file_path}")
        else:
            print(f"Empty file path for asset: {asset_path}")
    
    if len(response_lines) == 0:
        print("No valid file paths found for response file")
        return False
    
    print(f"Response file would contain {len(response_lines)} entries:")
    for line in response_lines:
        print(f"  {line}")
    
    return True

def main():
    """主测试函数"""
    print("=== PAK打包修复测试 ===")
    
    # 项目路径
    project_root = r"E:\UnrealProjects\NeoLive"
    content_dir = os.path.join(project_root, "Content")
    
    # 测试资产路径（模拟从错误日志中提取的路径）
    test_asset_paths = [
        "/Game/Maps/TestMap1/NewMap",
        "/Game/Maps/TestMap1/NewDataAsset",
        "/Game/Maps/TestMap1/NewBlueprint",
        "/Game/Maps/TestMap1/NewBlueprint1",
        "/Game/Maps/TestMap1/NewMap.NewMap",  # 重复名称情况
    ]
    
    print(f"Project root: {project_root}")
    print(f"Content directory: {content_dir}")
    print(f"Test asset paths: {test_asset_paths}")
    print()
    
    # 执行路径转换
    file_paths = simulate_convert_asset_paths_to_file_paths(test_asset_paths, content_dir)
    
    print("\n=== 转换结果 ===")
    for asset_path, file_path in zip(test_asset_paths, file_paths):
        exists = os.path.exists(file_path) if file_path else False
        status = "✓ 存在" if exists else "✗ 不存在"
        print(f"{asset_path} -> {file_path} ({status})")
    
    print("\n=== 响应文件模拟 ===")
    success = simulate_create_response_file(test_asset_paths, file_paths)
    
    if success:
        print("✓ 响应文件创建成功 - 修复有效！")
    else:
        print("✗ 响应文件创建失败 - 需要进一步修复")

if __name__ == "__main__":
    main()
