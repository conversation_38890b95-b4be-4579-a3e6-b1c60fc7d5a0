#!/usr/bin/env python3
"""
测试NeoLive项目的Cook+PAK工作流程
"""

import os
import subprocess
import sys
import time

def run_command(cmd, cwd=None, timeout=300):
    """运行命令并返回结果"""
    print(f"执行命令: {cmd}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, timeout=timeout)
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"输出:\n{result.stdout}")
        if result.stderr:
            print(f"错误:\n{result.stderr}")
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print("命令执行超时")
        return False, "", "超时"
    except Exception as e:
        print(f"执行命令时出错: {e}")
        return False, "", str(e)

def test_cook_workflow():
    """测试完整的Cook+PAK工作流程"""
    print("=== 测试完整的Cook+PAK工作流程 ===")
    
    project_dir = r"E:\UnrealProjects\NeoLive"
    project_file = os.path.join(project_dir, "NeoLive.uproject")
    unreal_cmd = r"E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cmd.exe"
    unreal_pak = r"E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealPak.exe"
    
    # 检查文件存在性
    if not os.path.exists(project_file):
        print(f"错误: 项目文件不存在: {project_file}")
        return False
    
    if not os.path.exists(unreal_cmd):
        print(f"错误: UnrealEditor-Cmd不存在: {unreal_cmd}")
        return False
    
    if not os.path.exists(unreal_pak):
        print(f"错误: UnrealPak不存在: {unreal_pak}")
        return False
    
    print("✅ 所有必需文件都存在")
    
    # 步骤1: Cook资产
    print("\n=== 步骤1: Cook资产 ===")
    cook_cmd = f'"{unreal_cmd}" "{project_file}" -run=Cook -TargetPlatform=Windows -unversioned -stdout -unattended'
    
    cook_success, cook_stdout, cook_stderr = run_command(cook_cmd, cwd=project_dir, timeout=600)
    
    if not cook_success:
        print("❌ Cook失败")
        return False
    
    print("✅ Cook成功")
    
    # 检查Cook输出
    cooked_dir = os.path.join(project_dir, "Saved", "Cooked", "Windows", "NeoLive", "Content")
    if not os.path.exists(cooked_dir):
        print(f"❌ Cook输出目录不存在: {cooked_dir}")
        return False
    
    # 检查特定文件
    test_map_umap = os.path.join(cooked_dir, "Maps", "TestMap2", "TestMap2.umap")
    test_map_uexp = os.path.join(cooked_dir, "Maps", "TestMap2", "TestMap2.uexp")
    
    if not os.path.exists(test_map_umap):
        print(f"❌ Cook后的地图文件不存在: {test_map_umap}")
        return False
    
    if not os.path.exists(test_map_uexp):
        print(f"❌ Cook后的地图数据文件不存在: {test_map_uexp}")
        return False
    
    print("✅ Cook后的文件验证通过")
    
    # 步骤2: 创建PAK文件
    print("\n=== 步骤2: 创建PAK文件 ===")
    
    # 创建响应文件
    response_file = os.path.join(project_dir, "test_response.txt")
    output_pak = os.path.join(project_dir, "TestMap2_Auto.pak")
    
    response_content = f'''"{test_map_uexp}" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.uexp" -compress
"{test_map_umap}" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.umap" -compress'''
    
    try:
        with open(response_file, 'w', encoding='utf-8') as f:
            f.write(response_content)
        print(f"✅ 创建响应文件: {response_file}")
    except Exception as e:
        print(f"❌ 创建响应文件失败: {e}")
        return False
    
    # 执行UnrealPak
    pak_cmd = f'"{unreal_pak}" "{output_pak}" -create="{response_file}" -compressionformats=Oodle -compresslevel=4 -compressmethod=Kraken -platform=Windows'
    
    pak_success, pak_stdout, pak_stderr = run_command(pak_cmd, cwd=project_dir)
    
    # 清理响应文件
    try:
        if os.path.exists(response_file):
            os.remove(response_file)
    except:
        pass
    
    if not pak_success:
        print("❌ PAK创建失败")
        return False
    
    print("✅ PAK创建成功")
    
    # 验证PAK文件
    if not os.path.exists(output_pak):
        print(f"❌ PAK文件不存在: {output_pak}")
        return False
    
    pak_size = os.path.getsize(output_pak)
    print(f"✅ PAK文件大小: {pak_size} 字节")
    
    # 验证PAK内容
    print("\n=== 步骤3: 验证PAK内容 ===")
    list_cmd = f'"{unreal_pak}" "{output_pak}" -List'
    
    list_success, list_stdout, list_stderr = run_command(list_cmd, cwd=project_dir)
    
    if not list_success:
        print("❌ PAK内容验证失败")
        return False
    
    print("✅ PAK内容验证成功")
    
    # 检查是否包含预期文件
    if "TestMap2.umap" in list_stdout and "TestMap2.uexp" in list_stdout:
        print("✅ PAK包含预期的地图文件")
    else:
        print("❌ PAK不包含预期的地图文件")
        return False
    
    print(f"\n🎉 完整的Cook+PAK工作流程测试成功!")
    print(f"生成的PAK文件: {output_pak}")
    print(f"文件大小: {pak_size} 字节")
    
    return True

def main():
    """主函数"""
    print("=== NeoLive Cook+PAK 工作流程测试 ===")
    print(f"当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_cook_workflow()
    
    if success:
        print("\n✅ 所有测试通过!")
        return True
    else:
        print("\n❌ 测试失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
