
// C++测试代码 - 可以添加到游戏模块中进行测试

#include "PakManager/NeoPakManager.h"
#include "Config/NeoMapPakConfig.h"

void TestPakCreation()
{
    // 获取PAK管理器实例
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get PAK Manager instance"));
        return;
    }
    
    // 测试资产路径
    TArray<FString> AssetPaths = {
        TEXT("/Game/Maps/TestMap1/NewMap"),
        TEXT("/Game/Maps/TestMap1/NewDataAsset"),
        TEXT("/Game/Maps/TestMap1/NewBlueprint"),
        TEXT("/Game/Maps/TestMap1/NewBlueprint1")
    };
    
    // 输出路径
    FString OutputPath = TEXT("E:/UnrealProjects/NeoLive/Paks/TestMaps/TestMap1.pak");
    
    UE_LOG(LogTemp, Log, TEXT("Starting PAK creation test..."));
    UE_LOG(LogTemp, Log, TEXT("Output path: %s"), *OutputPath);
    
    // 创建PAK文件
    bool bSuccess = PakManager->CreatePakFile(AssetPaths, OutputPath);
    
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("✓ PAK file created successfully!"));
        UE_LOG(LogTemp, Log, TEXT("PAK file location: %s"), *OutputPath);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("✗ PAK file creation failed!"));
    }
}

void TestPakCreationWithConfig()
{
    // 创建地图PAK配置
    UNeoMapPakConfig* MapConfig = NewObject<UNeoMapPakConfig>();
    MapConfig->ConfigName = TEXT("TestMap1_Config");
    MapConfig->OutputPakFileName = TEXT("TestMap1.pak");
    MapConfig->OutputDirectory.Path = TEXT("Paks/TestMaps");
    
    // 设置地图资产
    MapConfig->Map = TSoftObjectPtr<UWorld>(FSoftObjectPath(TEXT("/Game/Maps/TestMap1/NewMap")));
    
    // 获取PAK管理器并执行打包
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (PakManager)
    {
        bool bSuccess = PakManager->PackageFromConfig(MapConfig);
        
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("✓ Config-based PAK creation successful!"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("✗ Config-based PAK creation failed!"));
        }
    }
}
