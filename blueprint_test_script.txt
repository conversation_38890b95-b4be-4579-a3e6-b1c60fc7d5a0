
// Blueprint伪代码 - 测试PAK打包功能
// 这个代码需要在UE5编辑器中的Blueprint中实现

Event BeginPlay
{
    // 获取PAK管理器实例
    PakManager = Get Neo Pak Manager Instance
    
    if (PakManager == null)
    {
        Print String "Failed to get PAK Manager instance"
        return
    }
    
    // 创建测试资产路径数组
    AssetPaths = [
        "/Game/Maps/TestMap1/NewMap",
        "/Game/Maps/TestMap1/NewDataAsset", 
        "/Game/Maps/TestMap1/NewBlueprint",
        "/Game/Maps/TestMap1/NewBlueprint1"
    ]
    
    // 设置输出路径
    OutputPath = "E:/UnrealProjects/NeoLive/Paks/TestMaps/TestMap1.pak"
    
    Print String "Starting PAK creation test..."
    Print String ("Output path: " + OutputPath)
    
    // 调用PAK创建函数
    bool Success = PakManager.CreatePakFile(AssetPaths, OutputPath)
    
    if (Success)
    {
        Print String "✓ PAK file created successfully!"
        Print String ("PAK file location: " + OutputPath)
    }
    else
    {
        Print String "✗ PAK file creation failed!"
    }
}

// 或者使用配置资产的方式
Event TestWithConfig
{
    // 创建地图PAK配置
    MapConfig = Create Object (Neo Map Pak Config)
    MapConfig.ConfigName = "TestMap1_Config"
    MapConfig.OutputPakFileName = "TestMap1.pak"
    MapConfig.OutputDirectory = "Paks/TestMaps"
    MapConfig.Map = Load Object "/Game/Maps/TestMap1/NewMap"
    
    // 获取PAK管理器并执行打包
    PakManager = Get Neo Pak Manager Instance
    bool Success = PakManager.PackageFromConfig(MapConfig)
    
    if (Success)
    {
        Print String "✓ Config-based PAK creation successful!"
    }
    else
    {
        Print String "✗ Config-based PAK creation failed!"
    }
}
