@echo off
echo === NeoPakTools Cook+PAK 工作流程测试 ===
echo.

set PROJECT_DIR=E:\UnrealProjects\NeoLive
set PROJECT_FILE=%PROJECT_DIR%\NeoLive.uproject
set UNREAL_CMD=E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cmd.exe
set UNREAL_PAK=E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealPak.exe

echo 检查环境...
if not exist "%PROJECT_FILE%" (
    echo 错误: 项目文件不存在 %PROJECT_FILE%
    pause
    exit /b 1
)

if not exist "%UNREAL_CMD%" (
    echo 错误: UnrealEditor-Cmd.exe 不存在 %UNREAL_CMD%
    pause
    exit /b 1
)

if not exist "%UNREAL_PAK%" (
    echo 错误: UnrealPak.exe 不存在 %UNREAL_PAK%
    pause
    exit /b 1
)

echo 环境检查通过!
echo.

echo === 步骤1: 执行Cook ===
echo 正在Cook资产...
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -unversioned -fileopenlog -stdout -CrashForUAT -unattended -NoLogTimes

if %ERRORLEVEL% neq 0 (
    echo Cook失败!
    pause
    exit /b 1
)

echo Cook完成!
echo.

echo === 步骤2: 检查Cook输出 ===
set COOKED_DIR=%PROJECT_DIR%\Saved\Cooked\Windows\NeoLive\Content
if exist "%COOKED_DIR%" (
    echo Cook输出目录存在: %COOKED_DIR%
    dir "%COOKED_DIR%" /s /b | find /c ".u" > temp_count.txt
    set /p FILE_COUNT=<temp_count.txt
    del temp_count.txt
    echo 找到Cook后的文件
) else (
    echo 警告: Cook输出目录不存在
)

echo.

echo === 步骤3: 创建测试PAK ===
echo 创建测试响应文件...

set RESPONSE_FILE=%PROJECT_DIR%\test_response.txt
set OUTPUT_PAK=%PROJECT_DIR%\test_output.pak

echo "E:\UnrealProjects\NeoLive\Saved\Cooked\Windows\NeoLive\Content\Maps\TestMap2\TestMap2.uexp" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.uexp" -compress > "%RESPONSE_FILE%"
echo "E:\UnrealProjects\NeoLive\Saved\Cooked\Windows\NeoLive\Content\Maps\TestMap2\TestMap2.umap" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.umap" -compress >> "%RESPONSE_FILE%"

echo 执行UnrealPak...
"%UNREAL_PAK%" "%OUTPUT_PAK%" -create="%RESPONSE_FILE%" -compressionformats=Oodle -compresslevel=4 -compressmethod=Kraken -platform=Windows

if %ERRORLEVEL% neq 0 (
    echo UnrealPak失败!
    pause
    exit /b 1
)

echo UnrealPak完成!

if exist "%OUTPUT_PAK%" (
    echo 成功创建PAK文件: %OUTPUT_PAK%
    for %%A in ("%OUTPUT_PAK%") do echo 文件大小: %%~zA 字节
) else (
    echo 错误: PAK文件未创建
)

echo.
echo === 测试完成 ===
echo 清理临时文件...
if exist "%RESPONSE_FILE%" del "%RESPONSE_FILE%"

echo.
echo ✅ Cook+PAK工作流程测试完成!
pause
