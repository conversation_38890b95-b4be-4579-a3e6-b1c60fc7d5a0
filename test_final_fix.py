#!/usr/bin/env python3
"""
最终测试脚本：验证PAK打包修复是否完全有效
"""

import os
import sys

def test_final_path_conversion():
    """测试最终的路径转换逻辑"""
    
    # 项目路径
    project_root = r"E:\UnrealProjects\NeoLive"
    content_dir = os.path.join(project_root, "Content")
    
    # 测试资产路径（从错误日志中提取的实际路径）
    test_asset_paths = [
        "/Game/Maps/TestMap1/NewMap.NewMap",  # 重复名称情况
        "/Game/Maps/TestMap1/NewDataAsset",   # 普通资产
        "/Game/Maps/TestMap1/NewMap",         # 地图资产
        "/Game/Maps/TestMap1/NewBlueprint",   # Blueprint资产
        "/Game/Maps/TestMap1/NewBlueprint1",  # 另一个Blueprint资产
    ]
    
    print("=== 最终PAK打包修复测试 ===")
    print(f"项目根目录: {project_root}")
    print(f"Content目录: {content_dir}")
    print()
    
    all_success = True
    
    for asset_path in test_asset_paths:
        print(f"测试资产路径: {asset_path}")
        
        # 模拟ConvertAssetPathAlternativeInternal函数
        success = test_alternative_conversion(asset_path, content_dir)
        
        if success:
            print(f"  ✓ 转换成功")
        else:
            print(f"  ✗ 转换失败")
            all_success = False
        
        print()
    
    print("=== 测试结果 ===")
    if all_success:
        print("✓ 所有路径转换测试通过！修复有效！")
        print("现在应该不会再出现 'Source file does not exist' 错误")
    else:
        print("✗ 部分测试失败，需要进一步检查")
    
    return all_success

def test_alternative_conversion(asset_path, content_dir):
    """模拟ConvertAssetPathAlternativeInternal函数"""
    
    # 确保Content目录路径是绝对路径且格式正确
    content_dir = os.path.abspath(content_dir)
    content_dir = os.path.normpath(content_dir)
    
    print(f"  Content目录: {content_dir}")
    
    # Remove /Game/ prefix
    relative_path = asset_path
    if relative_path.startswith("/Game/"):
        relative_path = relative_path[6:]  # Remove "/Game/"
        print(f"  移除/Game/前缀，相对路径: {relative_path}")
    
    # Handle special case for assets with duplicate names (like Maps/NewMap.NewMap)
    if "." in relative_path:
        parts = relative_path.split(".")
        if len(parts) == 2:
            base_name = parts[0]
            extension = parts[1]
            
            # Check if the extension matches the base name (like NewMap.NewMap)
            base_file_name = os.path.basename(base_name)
            if extension == base_file_name:
                # This is likely a map or similar asset, use just the base path
                relative_path = base_name
                print(f"  检测到重复名称格式，使用基础路径: {relative_path}")
    
    # Try different file extensions
    possible_extensions = [".umap", ".uasset", ".ubulk"]
    
    for extension in possible_extensions:
        # 使用正确的路径组合方式，避免相对路径问题
        test_path = os.path.join(content_dir, relative_path.replace("/", os.sep) + extension)
        # 规范化路径，移除任何相对路径符号
        test_path = os.path.normpath(test_path)
        
        print(f"  测试路径: {test_path}")
        
        if os.path.exists(test_path):
            print(f"  找到替代文件路径: {test_path}")
            return True
    
    print(f"  未找到替代文件路径")
    return False

def check_response_file_format():
    """检查响应文件格式是否正确"""
    
    print("=== 响应文件格式测试 ===")
    
    # 模拟资产路径和文件路径
    test_cases = [
        ("/Game/Maps/TestMap1/NewMap", r"E:\UnrealProjects\NeoLive\Content\Maps\TestMap1\NewMap.umap"),
        ("/Game/Maps/TestMap1/NewDataAsset", r"E:\UnrealProjects\NeoLive\Content\Maps\TestMap1\NewDataAsset.uasset"),
        ("/Game/Maps/TestMap1/NewBlueprint", r"E:\UnrealProjects\NeoLive\Content\Maps\TestMap1\NewBlueprint.uasset"),
    ]
    
    print("期望的响应文件格式:")
    for asset_path, file_path in test_cases:
        # 将资产路径转换为PAK内的路径格式
        pak_internal_path = asset_path
        if pak_internal_path.startswith("/Game/"):
            pak_internal_path = pak_internal_path[6:]  # 移除"/Game/"前缀
        
        response_line = f'"{file_path}" "{pak_internal_path}"'
        print(f"  {response_line}")
    
    print()
    print("这种格式应该能被UnrealPak正确解析，不会产生相对路径错误")

def main():
    """主函数"""
    print("开始最终修复验证测试...")
    print()
    
    # 测试路径转换
    conversion_success = test_final_path_conversion()
    
    # 测试响应文件格式
    check_response_file_format()
    
    print("\n=== 总结 ===")
    if conversion_success:
        print("✓ 修复验证成功！")
        print("主要修复内容:")
        print("1. 修复了UNeoPakManager::ConvertAssetPathsToFilePaths函数")
        print("2. 添加了ConvertAssetPathAlternativeInternal函数处理特殊情况")
        print("3. 正确处理重复名称格式（如NewMap.NewMap）")
        print("4. 确保路径规范化，避免相对路径问题")
        print("5. 修复了响应文件格式")
        print()
        print("现在PAK打包应该能正常工作，不会再出现文件不存在的错误！")
    else:
        print("✗ 仍有问题需要解决")

if __name__ == "__main__":
    main()
