#!/usr/bin/env python3
"""
测试新的Cook+PAK打包流程
"""

import os
import subprocess
import sys
import time

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    print(f"执行命令: {cmd}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, timeout=300)
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"输出:\n{result.stdout}")
        if result.stderr:
            print(f"错误:\n{result.stderr}")
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print("命令执行超时")
        return False, "", "超时"
    except Exception as e:
        print(f"执行命令时出错: {e}")
        return False, "", str(e)

def test_cook_command():
    """测试Cook命令"""
    print("\n=== 测试Cook命令 ===")
    
    # 检查UnrealEditor-Cmd是否存在
    unreal_cmd_path = r"E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cmd.exe"
    if not os.path.exists(unreal_cmd_path):
        print(f"错误: UnrealEditor-Cmd.exe 不存在于 {unreal_cmd_path}")
        return False
    
    print(f"找到 UnrealEditor-Cmd: {unreal_cmd_path}")
    
    # 检查项目文件
    project_path = r"E:\UnrealProjects\NeoLive\NeoLive.uproject"
    if not os.path.exists(project_path):
        print(f"错误: 项目文件不存在于 {project_path}")
        return False
    
    print(f"找到项目文件: {project_path}")
    
    # 构建Cook命令
    cook_cmd = f'"{unreal_cmd_path}" "{project_path}" -run=Cook -TargetPlatform=Windows -unversioned -fileopenlog -stdout -CrashForUAT -unattended -NoLogTimes'
    
    print("\n开始Cook测试...")
    success, stdout, stderr = run_command(cook_cmd, cwd=r"E:\UnrealProjects\NeoLive")
    
    if success:
        print("Cook命令执行成功!")
        
        # 检查Cook输出目录
        cooked_dir = r"E:\UnrealProjects\NeoLive\Saved\Cooked\Windows\NeoLive\Content"
        if os.path.exists(cooked_dir):
            print(f"Cook输出目录存在: {cooked_dir}")
            
            # 列出Cook后的文件
            try:
                files = os.listdir(cooked_dir)
                print(f"Cook后的文件数量: {len(files)}")
                if files:
                    print("部分Cook后的文件:")
                    for i, file in enumerate(files[:10]):  # 只显示前10个文件
                        print(f"  {file}")
                    if len(files) > 10:
                        print(f"  ... 还有 {len(files) - 10} 个文件")
            except Exception as e:
                print(f"列出Cook文件时出错: {e}")
        else:
            print(f"警告: Cook输出目录不存在: {cooked_dir}")
    else:
        print("Cook命令执行失败!")
    
    return success

def test_unrealpak_command():
    """测试UnrealPak命令"""
    print("\n=== 测试UnrealPak命令 ===")
    
    # 检查UnrealPak是否存在
    unrealpak_path = r"E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealPak.exe"
    if not os.path.exists(unrealpak_path):
        print(f"错误: UnrealPak.exe 不存在于 {unrealpak_path}")
        return False
    
    print(f"找到 UnrealPak: {unrealpak_path}")
    
    # 创建测试响应文件
    test_response_content = '''
"E:\\UnrealProjects\\NeoLive\\Saved\\Cooked\\Windows\\NeoLive\\Content\\Maps\\TestMap2\\TestMap2.uexp" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.uexp" -compress
"E:\\UnrealProjects\\NeoLive\\Saved\\Cooked\\Windows\\NeoLive\\Content\\Maps\\TestMap2\\TestMap2.umap" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.umap" -compress
'''
    
    response_file = r"E:\UnrealProjects\NeoLive\test_response.txt"
    try:
        with open(response_file, 'w', encoding='utf-8') as f:
            f.write(test_response_content.strip())
        print(f"创建测试响应文件: {response_file}")
    except Exception as e:
        print(f"创建响应文件失败: {e}")
        return False
    
    # 构建UnrealPak命令
    output_pak = r"E:\UnrealProjects\NeoLive\test_output.pak"
    pak_cmd = f'"{unrealpak_path}" "{output_pak}" -create="{response_file}" -compressionformats=Oodle -compresslevel=4 -compressmethod=Kraken -platform=Windows'
    
    print("\n开始UnrealPak测试...")
    success, stdout, stderr = run_command(pak_cmd, cwd=r"E:\UnrealProjects\NeoLive")
    
    # 清理测试文件
    try:
        if os.path.exists(response_file):
            os.remove(response_file)
        if os.path.exists(output_pak):
            print(f"生成的测试PAK文件: {output_pak}")
            file_size = os.path.getsize(output_pak)
            print(f"PAK文件大小: {file_size} 字节")
            # 可以选择删除测试PAK文件
            # os.remove(output_pak)
    except Exception as e:
        print(f"清理文件时出错: {e}")
    
    if success:
        print("UnrealPak命令执行成功!")
    else:
        print("UnrealPak命令执行失败!")
    
    return success

def main():
    """主函数"""
    print("=== NeoPakTools Cook+PAK 工作流程测试 ===")
    print(f"当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查基本环境
    print("\n=== 环境检查 ===")
    project_dir = r"E:\UnrealProjects\NeoLive"
    if not os.path.exists(project_dir):
        print(f"错误: 项目目录不存在: {project_dir}")
        return False
    
    print(f"项目目录: {project_dir}")
    
    # 测试Cook命令
    cook_success = test_cook_command()
    
    # 测试UnrealPak命令
    pak_success = test_unrealpak_command()
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"Cook测试: {'成功' if cook_success else '失败'}")
    print(f"UnrealPak测试: {'成功' if pak_success else '失败'}")
    
    if cook_success and pak_success:
        print("\n✅ 所有测试通过! Cook+PAK工作流程可以正常工作。")
        return True
    else:
        print("\n❌ 部分测试失败，需要检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
