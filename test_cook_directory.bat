@echo off
echo === 测试Cook指定目录功能 ===
echo.

set PROJECT_FILE=E:\UnrealProjects\NeoLive\NeoLive.uproject
set UNREAL_CMD=E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cmd.exe

echo 检查环境...
if not exist "%PROJECT_FILE%" (
    echo 错误: 项目文件不存在
    pause
    exit /b 1
)

if not exist "%UNREAL_CMD%" (
    echo 错误: UnrealEditor-Cmd不存在
    pause
    exit /b 1
)

echo 环境检查通过!
echo.

echo === 测试1: Cook指定目录 Maps/TestMap1 ===
echo 正在Cook Maps/TestMap1目录...
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -CookDir=/Game/Maps/TestMap1 -unversioned -stdout -unattended

if %ERRORLEVEL% neq 0 (
    echo Cook目录失败!
    pause
    exit /b 1
)

echo Cook目录完成!
echo.

echo === 测试2: Cook指定地图 TestMap1 ===
echo 正在Cook TestMap1地图...
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -Map=TestMap1 -unversioned -stdout -unattended

if %ERRORLEVEL% neq 0 (
    echo Cook地图失败!
    pause
    exit /b 1
)

echo Cook地图完成!
echo.

echo === 检查Cook输出 ===
set COOKED_DIR=E:\UnrealProjects\NeoLive\Saved\Cooked\Windows\NeoLive\Content\Maps\TestMap1
if exist "%COOKED_DIR%" (
    echo Cook输出目录存在: %COOKED_DIR%
    dir "%COOKED_DIR%" /b
) else (
    echo 警告: Cook输出目录不存在
)

echo.
echo ✅ Cook指定目录测试完成!
pause
