# NeoPakTools Runtime PAK使用指南

## 概述

NeoPakTools插件现在完全支持runtime下的PAK文件加载和使用。您可以在游戏运行时动态加载和卸载PAK文件，访问其中的资产。

## 🚀 主要功能

### 1. Runtime PAK加载
- ✅ 动态挂载PAK文件到游戏文件系统
- ✅ 自动处理PAK优先级
- ✅ 支持多种挂载方式（FCoreDelegates + FPakPlatformFile）

### 2. Runtime PAK卸载
- ✅ 安全卸载PAK文件
- ✅ 清理内部状态
- ✅ 防止内存泄漏

### 3. 资产访问
- ✅ 从PAK中加载资产
- ✅ 验证PAK完整性
- ✅ 查询PAK状态

## 📋 API参考

### 核心加载/卸载功能

```cpp
// C++ API
UNeoPakManager* PakManager = UNeoPakManager::GetInstance();

// 加载PAK文件
bool bSuccess = PakManager->LoadPakFile(TEXT("E:/MyGame/Paks/TestMap1.pak"));

// 卸载PAK文件
bool bUnloaded = PakManager->UnloadPakFile(TEXT("E:/MyGame/Paks/TestMap1.pak"));
```

### 状态查询功能

```cpp
// 检查PAK是否已加载
bool bIsLoaded = PakManager->IsPakFileLoaded(TEXT("E:/MyGame/Paks/TestMap1.pak"));

// 获取所有已加载的PAK文件
TArray<FString> LoadedPaks = PakManager->GetLoadedPakFiles();

// 验证PAK完整性
bool bIsValid = PakManager->VerifyPakIntegrity(TEXT("E:/MyGame/Paks/TestMap1.pak"));
```

### 资产加载功能

```cpp
// 从PAK中加载资产
UObject* LoadedAsset = nullptr;
bool bAssetLoaded = PakManager->LoadAssetFromPak(
    TEXT("/Game/Maps/TestMap1/NewMap"), 
    LoadedAsset
);

if (bAssetLoaded && LoadedAsset)
{
    // 使用加载的资产
    UWorld* LoadedMap = Cast<UWorld>(LoadedAsset);
    if (LoadedMap)
    {
        // 切换到加载的地图
        UGameplayStatics::OpenLevel(this, FName(*LoadedMap->GetName()));
    }
}
```

## 🎮 Blueprint使用示例

### 1. 基本PAK加载

```blueprint
Event BeginPlay
├── Get Neo Pak Manager Instance
├── Load Pak File (PakFilePath: "E:/MyGame/Paks/TestMap1.pak")
├── Branch (Condition: Return Value)
    ├── True: Print String "PAK loaded successfully!"
    └── False: Print String "Failed to load PAK!"
```

### 2. 动态内容加载系统

```blueprint
// 自定义事件：Load DLC Content
Event LoadDLCContent (DLC Name: String)
├── Get Neo Pak Manager Instance
├── Format String (Format: "E:/MyGame/DLC/{0}.pak", Args: [DLC Name])
├── Load Pak File (PakFilePath: Formatted String)
├── Branch (Condition: Success)
    ├── True: 
    │   ├── Load Asset From Pak (AssetPath: "/Game/DLC/" + DLC Name + "/MainAsset")
    │   └── Trigger DLC Content Available Event
    └── False: Show Error Message
```

### 3. PAK状态监控

```blueprint
// 定时器事件：Monitor PAK Status
Event MonitorPAKStatus
├── Get Neo Pak Manager Instance
├── Get Loaded Pak Files
├── For Each Loop (Array: Loaded Pak Files)
    ├── Verify Pak Integrity (PakFilePath: Array Element)
    ├── Branch (Condition: Is Valid)
        ├── True: Continue
        └── False: 
            ├── Print String "PAK corrupted: " + Array Element
            └── Unload Pak File (PakFilePath: Array Element)
```

## 🔧 实际使用场景

### 1. DLC内容加载

```cpp
// DLC管理器示例
class MYGAME_API UDLCManager : public UObject
{
public:
    UFUNCTION(BlueprintCallable)
    bool LoadDLC(const FString& DLCName)
    {
        FString DLCPakPath = FString::Printf(TEXT("%s/DLC/%s.pak"), 
            *FPaths::ProjectDir(), *DLCName);
        
        UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
        
        // 验证PAK完整性
        if (!PakManager->VerifyPakIntegrity(DLCPakPath))
        {
            UE_LOG(LogTemp, Error, TEXT("DLC PAK is corrupted: %s"), *DLCName);
            return false;
        }
        
        // 加载PAK
        if (PakManager->LoadPakFile(DLCPakPath))
        {
            UE_LOG(LogTemp, Log, TEXT("DLC loaded successfully: %s"), *DLCName);
            
            // 加载DLC主资产
            UObject* DLCAsset = nullptr;
            FString DLCAssetPath = FString::Printf(TEXT("/Game/DLC/%s/MainAsset"), *DLCName);
            
            if (PakManager->LoadAssetFromPak(DLCAssetPath, DLCAsset))
            {
                // 注册DLC内容
                RegisterDLCContent(DLCName, DLCAsset);
                return true;
            }
        }
        
        return false;
    }
};
```

### 2. 地图流式加载

```cpp
// 地图流式加载示例
UFUNCTION(BlueprintCallable)
bool LoadMapFromPak(const FString& MapName)
{
    FString MapPakPath = FString::Printf(TEXT("%s/Maps/%s.pak"), 
        *FPaths::ProjectDir(), *MapName);
    
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    
    // 加载地图PAK
    if (PakManager->LoadPakFile(MapPakPath))
    {
        // 加载地图资产
        UObject* MapAsset = nullptr;
        FString MapAssetPath = FString::Printf(TEXT("/Game/Maps/%s/%s"), *MapName, *MapName);
        
        if (PakManager->LoadAssetFromPak(MapAssetPath, MapAsset))
        {
            UWorld* MapWorld = Cast<UWorld>(MapAsset);
            if (MapWorld)
            {
                // 切换到新地图
                UGameplayStatics::OpenLevel(this, FName(*MapName));
                return true;
            }
        }
    }
    
    return false;
}
```

## ⚠️ 重要注意事项

### 1. PAK文件路径
- 确保PAK文件路径是绝对路径
- 建议将PAK文件放在项目的`Paks`目录下
- 路径分隔符使用正斜杠`/`或反斜杠`\`都可以

### 2. 资产路径格式
- 使用UE5标准的资产路径格式：`/Game/...`
- 确保资产路径与PAK中的实际路径匹配
- 不需要包含文件扩展名（`.uasset`, `.umap`等）

### 3. 内存管理
- 及时卸载不需要的PAK文件以释放内存
- 监控PAK文件的完整性
- 避免重复加载同一个PAK文件

### 4. 错误处理
- 始终检查加载操作的返回值
- 实现适当的错误处理和用户反馈
- 记录详细的日志信息用于调试

## 🎯 最佳实践

1. **预加载验证**：在加载PAK前先验证其完整性
2. **异步加载**：对于大型PAK文件，考虑在后台线程加载
3. **缓存管理**：实现PAK文件的缓存和清理策略
4. **版本控制**：为PAK文件添加版本信息和兼容性检查
5. **安全性**：验证PAK文件的来源和完整性

## 🔍 调试和故障排除

### 常见问题

1. **PAK加载失败**
   - 检查文件路径是否正确
   - 验证PAK文件是否存在且未损坏
   - 查看日志中的详细错误信息

2. **资产加载失败**
   - 确认资产路径格式正确
   - 检查PAK是否已成功挂载
   - 验证资产确实存在于PAK中

3. **内存问题**
   - 及时卸载不需要的PAK
   - 监控内存使用情况
   - 避免循环引用

### 日志查看

在UE5编辑器或游戏中，查看`LogNeoPakTools`类别的日志：
```
LogNeoPakTools: Log: Successfully loaded PAK file: E:/MyGame/Paks/TestMap1.pak
LogNeoPakTools: Log: Successfully loaded asset: /Game/Maps/TestMap1/NewMap
```

现在您的PAK文件可以在runtime下完全正常使用了！🎉
