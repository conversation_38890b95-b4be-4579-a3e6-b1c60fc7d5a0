@echo off
echo === Testing Cook Config Asset Directories ===
echo.

set PROJECT_FILE=E:\UnrealProjects\NeoLive\NeoLive.uproject
set UNREAL_CMD=E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cmd.exe

echo Checking environment...
if not exist "%PROJECT_FILE%" (
    echo ERROR: Project file not found
    pause
    exit /b 1
)

if not exist "%UNREAL_CMD%" (
    echo ERROR: UnrealEditor-Cmd not found
    pause
    exit /b 1
)

echo Environment check passed!
echo.

echo === Test 1: Cook Maps/TestMap1 directory ===
echo Cooking Maps/TestMap1 directory...
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -CookDir=/Game/Maps/TestMap1 -unversioned -stdout -unattended

if %ERRORLEVEL% neq 0 (
    echo Cook Maps/TestMap1 failed!
    pause
    exit /b 1
)

echo Cook Maps/TestMap1 completed successfully!
echo.

echo === Test 2: Cook Maps/TestMap2 directory ===
echo Cooking Maps/TestMap2 directory...
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -CookDir=/Game/Maps/TestMap2 -unversioned -stdout -unattended

if %ERRORLEVEL% neq 0 (
    echo Cook Maps/TestMap2 failed!
    pause
    exit /b 1
)

echo Cook Maps/TestMap2 completed successfully!
echo.

echo === Test 3: Cook multiple directories ===
echo Cooking multiple directories...
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -CookDir=/Game/Maps/TestMap1 -CookDir=/Game/Maps/TestMap2 -unversioned -stdout -unattended

if %ERRORLEVEL% neq 0 (
    echo Cook multiple directories failed!
    pause
    exit /b 1
)

echo Cook multiple directories completed successfully!
echo.

echo === Checking Cook output ===
set COOKED_DIR=E:\UnrealProjects\NeoLive\Saved\Cooked\Windows\NeoLive\Content\Maps
if exist "%COOKED_DIR%" (
    echo Cook output directory exists: %COOKED_DIR%
    echo.
    echo TestMap1 files:
    if exist "%COOKED_DIR%\TestMap1" (
        dir "%COOKED_DIR%\TestMap1" /b
    ) else (
        echo No TestMap1 directory found
    )
    echo.
    echo TestMap2 files:
    if exist "%COOKED_DIR%\TestMap2" (
        dir "%COOKED_DIR%\TestMap2" /b
    ) else (
        echo No TestMap2 directory found
    )
) else (
    echo WARNING: Cook output directory does not exist
)

echo.
echo === All tests completed successfully! ===
pause
