@echo off
echo Testing Cook Directory Feature...

set PROJECT_FILE=E:\UnrealProjects\NeoLive\NeoLive.uproject
set UNREAL_CMD=E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cmd.exe

echo Checking files...
if not exist "%PROJECT_FILE%" (
    echo Project file not found
    exit /b 1
)

if not exist "%UNREAL_CMD%" (
    echo UnrealEditor-Cmd not found
    exit /b 1
)

echo Files found!

echo Testing Cook Directory: Maps/TestMap1
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -CookDir=/Game/Maps/TestMap1 -unversioned -stdout -unattended

echo Cook directory completed with code %ERRORLEVEL%

echo Testing Cook Map: TestMap1
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -Map=TestMap1 -unversioned -stdout -unattended

echo Cook map completed with code %ERRORLEVEL%

echo Checking cooked output...
set COOKED_DIR=E:\UnrealProjects\NeoLive\Saved\Cooked\Windows\NeoLive\Content\Maps\TestMap1
if exist "%COOKED_DIR%" (
    echo Cooked directory exists: %COOKED_DIR%
    dir "%COOKED_DIR%" /b
) else (
    echo Warning: Cooked directory does not exist
)

echo Test completed!
pause
