@echo off
echo === 测试PAK创建 ===
echo.

set PROJECT_DIR=E:\UnrealProjects\NeoLive
set UNREAL_PAK=E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealPak.exe
set COOKED_DIR=%PROJECT_DIR%\Saved\Cooked\Windows\NeoLive\Content

echo 检查Cook后的文件...
if not exist "%COOKED_DIR%\Maps\TestMap2\TestMap2.umap" (
    echo 错误: Cook后的地图文件不存在
    pause
    exit /b 1
)

if not exist "%COOKED_DIR%\Maps\TestMap2\TestMap2.uexp" (
    echo 错误: Cook后的地图数据文件不存在
    pause
    exit /b 1
)

echo Cook后的文件检查通过!
echo.

echo 创建PAK响应文件...
set RESPONSE_FILE=%PROJECT_DIR%\pak_response.txt
set OUTPUT_PAK=%PROJECT_DIR%\TestMap2.pak

echo "%COOKED_DIR%\Maps\TestMap2\TestMap2.uexp" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.uexp" -compress > "%RESPONSE_FILE%"
echo "%COOKED_DIR%\Maps\TestMap2\TestMap2.umap" "../../../NeoLive/Content/Maps/TestMap2/TestMap2.umap" -compress >> "%RESPONSE_FILE%"

echo 响应文件内容:
type "%RESPONSE_FILE%"
echo.

echo 执行UnrealPak创建PAK文件...
"%UNREAL_PAK%" "%OUTPUT_PAK%" -create="%RESPONSE_FILE%" -compressionformats=Oodle -compresslevel=4 -compressmethod=Kraken -platform=Windows

if %ERRORLEVEL% neq 0 (
    echo PAK创建失败!
    pause
    exit /b 1
)

echo.
echo PAK创建成功!

if exist "%OUTPUT_PAK%" (
    echo PAK文件: %OUTPUT_PAK%
    for %%A in ("%OUTPUT_PAK%") do echo 文件大小: %%~zA 字节
    
    echo.
    echo 验证PAK文件内容...
    "%UNREAL_PAK%" "%OUTPUT_PAK%" -List
) else (
    echo 错误: PAK文件未创建
)

echo.
echo 清理临时文件...
if exist "%RESPONSE_FILE%" del "%RESPONSE_FILE%"

echo.
echo ✅ Cook+PAK工作流程测试完成!
pause
