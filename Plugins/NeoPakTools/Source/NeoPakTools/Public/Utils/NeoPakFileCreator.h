// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Config/NeoPakConfigAssetBase.h"

/**
 * Utility class for creating PAK files from asset configurations
 */
class NEOPAKTOOLS_API FNeoPakFileCreator
{
public:
    /** Create a PAK file from the given configuration */
    static bool CreatePakFromConfig(UNeoPakConfigAssetBase* ConfigAsset);

    /** Create a PAK file from asset paths */
    static bool CreatePakFromAssets(const TArray<FString>& AssetPaths, const FString& OutputPath);

    /** Get the UnrealPak executable path */
    static FString GetUnrealPakPath();

    /** Check if UnrealPak is available */
    static bool IsUnrealPakAvailable();

    /** Create a response file for UnrealPak */
    static bool CreateResponseFile(const TArray<FString>& AssetPaths, const FString& ResponseFilePath);

    /** Execute UnrealPak with the given parameters */
    static bool ExecuteUnrealPak(const FString& PakFilePath, const FString& ResponseFilePath, const FString& AdditionalArgs = TEXT(""));

    /** Convert asset paths to file system paths */
    static TArray<FString> ConvertAssetPathsToFilePaths(const TArray<FString>& AssetPaths);

    /** Get the content directory path */
    static FString GetContentDirectory();

    /** Validate that all assets exist */
    static bool ValidateAssetPaths(const TArray<FString>& AssetPaths, TArray<FString>& OutMissingAssets);

private:
    /** Generate a temporary response file path */
    static FString GenerateResponseFilePath(const FString& PakFileName);

    /** Clean up temporary files */
    static void CleanupTempFiles(const FString& ResponseFilePath);

    /** Validate asset using Asset Registry */
    static bool ValidateAssetUsingRegistry(const FString& AssetPath);

    /** Alternative asset path conversion for special cases */
    static FString ConvertAssetPathAlternative(const FString& AssetPath);
};
