// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Utils/NeoCustomCookTool.h"
#include "Utils/NeoPakFileCreator.h"
#include "NeoCookPakIntegration.generated.h"

USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoCookPakSettings
{
    GENERATED_BODY()

    /** Target platform for cooking and packaging */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString TargetPlatform = TEXT("Windows");

    /** Output directory for cooked assets */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString CookOutputDirectory;

    /** Output directory for PAK files */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString PakOutputDirectory;

    /** Whether to use unversioned cooking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUnversioned = true;

    /** Whether to compress cooked assets */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bCompressed = false;

    /** Whether to include dependencies when cooking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIncludeDependencies = true;

    /** Patterns to exclude from cooking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> ExcludePatterns;

    /** Use NeoPakFileCreator's Cook+PAK workflow instead of custom cooking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseNeoPakCreatorWorkflow = true;

    /** PAK compression format */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString CompressionFormat = TEXT("Oodle");

    /** PAK compression method */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString CompressionMethod = TEXT("Kraken");

    /** PAK compression level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 CompressionLevel = 4;

    FNeoCookPakSettings()
    {
        CookOutputDirectory = FPaths::ProjectSavedDir() / TEXT("NeoPakTools") / TEXT("Cooked");
        PakOutputDirectory = FPaths::ProjectDir() / TEXT("Paks");
    }
};

USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoCookPakResult
{
    GENERATED_BODY()

    /** Whether the entire process succeeded */
    UPROPERTY(BlueprintReadOnly)
    bool bSuccess = false;

    /** Whether cooking succeeded */
    UPROPERTY(BlueprintReadOnly)
    bool bCookSuccess = false;

    /** Whether PAK creation succeeded */
    UPROPERTY(BlueprintReadOnly)
    bool bPakSuccess = false;

    /** Number of assets cooked */
    UPROPERTY(BlueprintReadOnly)
    int32 CookedAssetCount = 0;

    /** List of successfully cooked assets */
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> CookedAssets;

    /** List of failed assets */
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> FailedAssets;

    /** Generated PAK file path */
    UPROPERTY(BlueprintReadOnly)
    FString GeneratedPakFile;

    /** Error message if any */
    UPROPERTY(BlueprintReadOnly)
    FString ErrorMessage;

    /** Detailed cook result */
    UPROPERTY(BlueprintReadOnly)
    FNeoCustomCookResult CookResult;
};

/**
 * Neo Cook+PAK Integration - Unified interface for cooking and packaging
 * Combines the functionality of NeoCustomCookTool and NeoPakFileCreator
 */
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoCookPakIntegration : public UObject
{
    GENERATED_BODY()

public:
    UNeoCookPakIntegration();

    /** Cook and package a directory using the integrated workflow */
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Cook+PAK")
    static FNeoCookPakResult CookAndPackageDirectory(const FString& DirectoryPath, const FNeoCookPakSettings& Settings);

    /** Cook and package specific assets using the integrated workflow */
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Cook+PAK")
    static FNeoCookPakResult CookAndPackageAssets(const TArray<FString>& AssetPaths, const FNeoCookPakSettings& Settings);

    /** Cook directory only (no PAK creation) */
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Cook+PAK")
    static FNeoCustomCookResult CookDirectoryOnly(const FString& DirectoryPath, const FNeoCookPakSettings& Settings);

    /** Create PAK from already cooked assets */
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Cook+PAK")
    static bool CreatePakFromCookedAssets(const FString& CookedDirectory, const FString& OutputPakFile, const FNeoCookPakSettings& Settings);

    /** Validate Cook+PAK settings */
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Cook+PAK")
    static bool ValidateSettings(const FNeoCookPakSettings& Settings, FString& OutErrorMessage);

    /** Get recommended settings for different use cases */
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Cook+PAK")
    static FNeoCookPakSettings GetRecommendedSettings(const FString& UseCase = TEXT("Default"));

private:
    /** Convert NeoCookPakSettings to NeoCustomCookSettings */
    static FNeoCustomCookSettings ConvertToCustomCookSettings(const FNeoCookPakSettings& Settings);

    /** Generate PAK file name from directory path */
    static FString GeneratePakFileName(const FString& DirectoryPath, const FNeoCookPakSettings& Settings);

    /** Clean up temporary files */
    static void CleanupTempFiles(const TArray<FString>& FilesToClean);
};
