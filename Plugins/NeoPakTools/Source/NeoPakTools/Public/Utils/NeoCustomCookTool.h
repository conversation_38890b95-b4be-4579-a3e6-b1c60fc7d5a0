// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/World.h"
#include "HAL/PlatformFilemanager.h"
#include "Interfaces/ITargetPlatform.h"
#include "Interfaces/ITargetPlatformManagerModule.h"
#include "UObject/SavePackage.h"
#include "UObject/Package.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "NeoCustomCookTool.generated.h"

USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoCustomCookSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString OutputDirectory;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString TargetPlatform = TEXT("Windows");

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUnversioned = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bCompressed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIncludeDependencies = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> ExcludePatterns;

    FNeoCustomCookSettings()
    {
        OutputDirectory = FPaths::ProjectSavedDir() / TEXT("NeoPakTools") / TEXT("CustomCooked");
    }
};

USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoCustomCookResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly)
    int32 CookedPackageCount = 0;

    UPROPERTY(BlueprintReadOnly)
    TArray<FString> CookedPackages;

    UPROPERTY(BlueprintReadOnly)
    TArray<FString> FailedPackages;

    UPROPERTY(BlueprintReadOnly)
    FString ErrorMessage;
};

/**
 * Neo Custom Cook Tool - Advanced cooking functionality for NeoPakTools
 * Provides fine-grained control over asset cooking process
 */
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoCustomCookTool : public UObject
{
    GENERATED_BODY()

public:
    UNeoCustomCookTool();

    // Main Cook interfaces
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook")
    static FNeoCustomCookResult CookDirectory(const FString& DirectoryPath, const FNeoCustomCookSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook")
    static FNeoCustomCookResult CookAssets(const TArray<FString>& AssetPaths, const FNeoCustomCookSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook")
    static FNeoCustomCookResult CookPackage(UPackage* Package, const FNeoCustomCookSettings& Settings);

    // Helper functions
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook")
    static TArray<FString> GetAssetsInDirectory(const FString& DirectoryPath, bool bRecursive = true);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook")
    static bool ValidateCookSettings(const FNeoCustomCookSettings& Settings, FString& OutErrorMessage);

    // Integration with NeoPakFileCreator
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook")
    static bool CookDirectoryWithNeoPakCreator(const FString& DirectoryPath, const FString& TargetPlatform = TEXT("Windows"));

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook")
    static bool CookAssetsWithNeoPakCreator(const TArray<FString>& AssetPaths, const FString& TargetPlatform = TEXT("Windows"));

private:
    // Internal implementation
    static bool InitializeCookEnvironment(const FNeoCustomCookSettings& Settings, const ITargetPlatform*& OutTargetPlatform);
    static bool CookSinglePackage(UPackage* Package, const ITargetPlatform* TargetPlatform, const FNeoCustomCookSettings& Settings, FString& OutError);
    static bool SaveCookedPackage(UPackage* Package, const ITargetPlatform* TargetPlatform, const FString& OutputPath, const FNeoCustomCookSettings& Settings);
    static void CollectDependencies(UPackage* Package, TArray<UPackage*>& OutDependencies);
    static FString GetCookedPackagePath(const FString& PackageName, const FNeoCustomCookSettings& Settings);
    static bool ShouldExcludePackage(const FString& PackagePath, const FNeoCustomCookSettings& Settings);
};
