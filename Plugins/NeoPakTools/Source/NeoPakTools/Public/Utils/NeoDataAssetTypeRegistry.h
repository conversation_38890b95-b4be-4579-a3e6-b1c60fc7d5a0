// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "NeoDataAssetTypeRegistry.generated.h"

// DataAsset类型信息
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoDataAssetTypeInfo
{
    GENERATED_BODY()

    // DataAsset类型名称
    UPROPERTY(BlueprintReadOnly)
    FString TypeName;

    // DataAsset类路径
    UPROPERTY(BlueprintReadOnly)
    FString ClassPath;

    // 是否需要骨骼依赖
    UPROPERTY(BlueprintReadOnly)
    bool bRequiresSkeleton = false;

    // 类型描述
    UPROPERTY(BlueprintReadOnly)
    FString Description;

    // 配置资产路径（在PAK中的路径）
    UPROPERTY(BlueprintReadOnly)
    FString ConfigAssetPath;

    // 创建时间
    UPROPERTY(BlueprintReadOnly)
    FString CreationTime;

    // 默认构造函数
    FNeoDataAssetTypeInfo()
    {
        TypeName = TEXT("");
        ClassPath = TEXT("");
        bRequiresSkeleton = false;
        Description = TEXT("");
        ConfigAssetPath = TEXT("");
        CreationTime = TEXT("");
    }
};

class NEOPAKTOOLS_API FNeoDataAssetTypeRegistry
{
public:
    // 注册DataAsset类型信息到PAK文件
    static void RegisterDataAssetTypeInPak(const FString& PakFilePath, UNeoPakConfigAssetBase* ConfigAsset);

    // 从PAK文件读取DataAsset类型信息
    static FNeoDataAssetTypeInfo GetDataAssetTypeFromPak(const FString& PakFilePath);

    // 根据DataAsset获取类型信息
    static FNeoDataAssetTypeInfo GetDataAssetTypeInfo(UNeoPakConfigAssetBase* ConfigAsset);

    // 检查PAK文件是否包含DataAsset类型信息
    static bool HasDataAssetTypeInfo(const FString& PakFilePath);

    // 获取所有支持的DataAsset类型
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static TArray<FNeoDataAssetTypeInfo> GetAllSupportedTypes();

private:
    // DataAsset类型信息文件名（在PAK中）
    static const FString DataAssetTypeInfoFileName;

    // 序列化类型信息
    static FString SerializeTypeInfo(const FNeoDataAssetTypeInfo& TypeInfo);

    // 反序列化类型信息
    static FNeoDataAssetTypeInfo DeserializeTypeInfo(const FString& SerializedData);

    // 获取类型信息文件的完整路径
    static FString GetTypeInfoFilePath(const FString& PakFilePath);
};
