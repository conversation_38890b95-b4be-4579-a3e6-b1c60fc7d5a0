// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Utils/NeoCustomCookTool.h"
#include "Utils/NeoCookPakIntegration.h"
#include "NeoCookPakBlueprintLibrary.generated.h"

/**
 * Neo Cook+PAK Blueprint Library - Convenient Blueprint interfaces for cooking and packaging
 */
UCLASS()
class NEOPAKTOOLS_API UNeoCookPakBlueprintLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // Quick Cook+PAK interfaces
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Quick Cook+PAK", meta = (CallInEditor = "true"))
    static FNeoCookPakResult QuickCookAndPackageDirectory(const FString& DirectoryPath, 
                                                         const FString& TargetPlatform = TEXT("Windows"),
                                                         const FString& OutputDirectory = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Quick Cook+PAK", meta = (CallInEditor = "true"))
    static FNeoCookPakResult QuickCookAndPackageAssets(const TArray<FString>& AssetPaths,
                                                       const FString& TargetPlatform = TEXT("Windows"),
                                                       const FString& OutputDirectory = TEXT(""));

    // Custom Cook interfaces
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook", meta = (CallInEditor = "true"))
    static FNeoCustomCookResult QuickCookDirectory(const FString& DirectoryPath, 
                                                   const FString& TargetPlatform = TEXT("Windows"),
                                                   const FString& OutputDirectory = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Custom Cook", meta = (CallInEditor = "true"))
    static FNeoCustomCookResult QuickCookAssets(const TArray<FString>& AssetPaths,
                                                const FString& TargetPlatform = TEXT("Windows"),
                                                const FString& OutputDirectory = TEXT(""));

    // NeoPakFileCreator interfaces
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|PAK Creator", meta = (CallInEditor = "true"))
    static bool QuickCookWithNeoPakCreator(const FString& DirectoryPath, const FString& TargetPlatform = TEXT("Windows"));

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|PAK Creator", meta = (CallInEditor = "true"))
    static bool QuickCreatePakFile(const TArray<FString>& AssetPaths, const FString& OutputPakFile);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Utilities")
    static TArray<FString> GetAvailableTargetPlatforms();

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Utilities")
    static TArray<FString> GetProjectDirectories();

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Utilities")
    static TArray<FString> GetAssetsInDirectory(const FString& DirectoryPath, bool bRecursive = true);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Utilities")
    static FNeoCookPakSettings GetRecommendedSettingsForUseCase(const FString& UseCase = TEXT("Default"));

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Utilities")
    static bool ValidateCookPakSettings(const FNeoCookPakSettings& Settings, FString& OutErrorMessage);

    // Advanced interfaces
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Advanced", meta = (CallInEditor = "true"))
    static FNeoCookPakResult CookAndPackageWithCustomSettings(const FString& DirectoryPath, const FNeoCookPakSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Advanced", meta = (CallInEditor = "true"))
    static FNeoCustomCookResult CookDirectoryOnly(const FString& DirectoryPath, const FNeoCookPakSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Advanced")
    static bool CreatePakFromCookedAssets(const FString& CookedDirectory, const FString& OutputPakFile, const FNeoCookPakSettings& Settings);
};
