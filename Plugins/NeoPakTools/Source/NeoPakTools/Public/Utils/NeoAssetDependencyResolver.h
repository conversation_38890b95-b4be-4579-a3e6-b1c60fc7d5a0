// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/SoftObjectPath.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "NeoAssetDependencyResolver.generated.h"

// 依赖检查结果
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoDependencyCheckResult
{
    GENERATED_BODY()

    // 检查是否通过
    UPROPERTY(BlueprintReadOnly)
    bool bCheckPassed = false;

    // 缺失的依赖资产
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> MissingDependencies;

    // 目录外的依赖资产（排除骨骼、插件、引擎资产）
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> OutOfDirectoryDependencies;

    // 错误信息
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> ErrorMessages;

    // 所有问题的汇总（包括缺失依赖、错误等）
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> Issues;
};

class NEOPAKTOOLS_API FNeoAssetDependencyResolver
{
public:
    // 解析资产依赖
    TArray<FString> ResolveDependencies(const FString& AssetPath);

    // 验证依赖完整性
    bool ValidateDependencies(const TArray<FString>& AssetPaths);

    // 获取打包顺序（基于DataAsset配置）
    TArray<UNeoPakConfigAssetBase*> GetPackagingOrder(const TArray<UNeoPakConfigAssetBase*>& ConfigAssets);

    // 检查DataAsset目录依赖（自动检查）
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    FNeoDependencyCheckResult CheckDataAssetDirectoryDependencies(UNeoPakConfigAssetBase* ConfigAsset, bool bAutoCheck = true);

    // 手动检查DataAsset依赖（右键菜单使用）
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    FNeoDependencyCheckResult ManualCheckDataAssetDependencies(UNeoPakConfigAssetBase* ConfigAsset);

private:
    // 检查DataAsset之间的依赖关系
    bool HasDependency(UNeoPakConfigAssetBase* ConfigA, UNeoPakConfigAssetBase* ConfigB);

    // 获取DataAsset所在目录
    FString GetDataAssetDirectory(UNeoPakConfigAssetBase* ConfigAsset);

    // 检查资产是否在指定目录下
    bool IsAssetInDirectory(const FString& AssetPath, const FString& DirectoryPath);

    // 检查是否为排除的资产类型（骨骼、插件、引擎资产）
    bool IsExcludedAsset(const FString& AssetPath);

    // 检查是否为骨骼资产
    bool IsSkeletonAsset(const FString& AssetPath);

    // 检查是否为插件资产
    bool IsPluginAsset(const FString& AssetPath);

    // 检查是否为引擎资产
    bool IsEngineAsset(const FString& AssetPath);
};
