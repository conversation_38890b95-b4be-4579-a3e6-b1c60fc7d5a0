// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "Engine/EngineTypes.h"
#include "NeoPakToolsSettings.generated.h"

// 压缩方法枚举
UENUM(BlueprintType)
enum class EPakCompressionMethod : uint8
{
    None        UMETA(DisplayName = "None"),
    Zlib        UMETA(DisplayName = "Zlib"),
    Gzip        UMETA(DisplayName = "Gzip"),
    LZ4         UMETA(DisplayName = "LZ4")
};

// 压缩设置结构
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakCompressionSettings
{
    GENERATED_BODY()

    // 是否启用压缩
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression")
    bool bEnableCompression = true;

    // 压缩级别 (0-9)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression", meta = (ClampMin = "0", ClampMax = "9", EditCondition = "bEnableCompression"))
    int32 CompressionLevel = 6;

    // 压缩方法
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression", meta = (EditCondition = "bEnableCompression"))
    EPakCompressionMethod CompressionMethod = EPakCompressionMethod::Zlib;

    // 压缩块大小 (KB)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression", meta = (ClampMin = "64", ClampMax = "1024", EditCondition = "bEnableCompression"))
    int32 CompressionBlockSize = 256;

    // 最小压缩节省百分比
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression", meta = (ClampMin = "0", ClampMax = "100", EditCondition = "bEnableCompression"))
    int32 MinCompressionPercentSaved = 5;
};

// 加密设置结构
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakEncryptionSettings
{
    GENERATED_BODY()

    // 是否启用加密
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encryption")
    bool bEnableEncryption = false;

    // 加密密钥
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encryption", meta = (EditCondition = "bEnableEncryption"))
    FString EncryptionKey;

    // 加密PAK索引
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encryption", meta = (EditCondition = "bEnableEncryption"))
    bool bEncryptPakIndex = true;

    // 加密INI文件
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encryption", meta = (EditCondition = "bEnableEncryption"))
    bool bEncryptIniFiles = true;

    // 加密UAsset文件
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encryption", meta = (EditCondition = "bEnableEncryption"))
    bool bEncryptUAssetFiles = false;

    // 加密所有文件
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encryption", meta = (EditCondition = "bEnableEncryption"))
    bool bEncryptAllFiles = false;
};

// 平台特定设置
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakPlatformSettings
{
    GENERATED_BODY()

    // 目标平台
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Platform")
    FString TargetPlatform = TEXT("Windows");

    // 平台特定压缩设置
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Platform")
    FNeoPakCompressionSettings PlatformCompressionSettings;

    // 平台特定加密设置
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Platform")
    FNeoPakEncryptionSettings PlatformEncryptionSettings;
};

// 插件全局配置
UCLASS(Config=Game, DefaultConfig)
class NEOPAKTOOLS_API UNeoPakToolsSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UNeoPakToolsSettings();

    // 默认输出目录
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Output Settings", meta = (DisplayName = "Default Output Directory"))
    FDirectoryPath DefaultOutputDirectory;
    
    // 是否使用相对路径
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Output Settings", meta = (DisplayName = "Use Relative Paths"))
    bool bUseRelativePaths = true;
    
    // 默认压缩设置
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Compression Settings", meta = (DisplayName = "Default Compression Settings"))
    FNeoPakCompressionSettings DefaultCompressionSettings;

    // 默认加密设置
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Security Settings", meta = (DisplayName = "Default Encryption Settings"))
    FNeoPakEncryptionSettings DefaultEncryptionSettings;

    // 平台特定设置
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Platform Settings", meta = (DisplayName = "Platform Specific Settings"))
    TArray<FNeoPakPlatformSettings> PlatformSettings;
    
    // 自动创建输出目录
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Output Settings", meta = (DisplayName = "Auto Create Output Directory"))
    bool bAutoCreateOutputDirectory = true;

    // 高级打包选项
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Advanced Settings", meta = (DisplayName = "Enable Incremental Packaging"))
    bool bEnableIncrementalPackaging = false;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Advanced Settings", meta = (DisplayName = "Generate PAK Signature"))
    bool bGeneratePakSignature = false;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Advanced Settings", meta = (DisplayName = "Verify PAK Integrity"))
    bool bVerifyPakIntegrity = true;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Advanced Settings", meta = (DisplayName = "Max Parallel PAK Creation"))
    int32 MaxParallelPakCreation = 1;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Advanced Settings", meta = (DisplayName = "Custom UnrealPak Arguments"))
    FString CustomUnrealPakArguments;

    // 获取完整的输出路径
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    FString GetFullOutputPath(const FString& FileName) const;
    
    // 获取设置实例
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static UNeoPakToolsSettings* GetNeoPakToolsSettings();

#if WITH_EDITOR
    virtual FText GetSectionText() const override;
    virtual FText GetSectionDescription() const override;
#endif
};
