// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "Config/NeoSkeletonPakConfig.h"
#include "Engine/SkeletalMesh.h"
#include "NeoClothingPakConfig.generated.h"

// 服装类型枚举
UENUM(BlueprintType)
enum class EClothingType : uint8
{
    Armor       UMETA(DisplayName = "Armor"),
    Casual      UMETA(DisplayName = "Casual"),
    Formal      UMETA(DisplayName = "Formal"),
    Fantasy     UMETA(DisplayName = "Fantasy"),
    SciFi       UMETA(DisplayName = "Sci-Fi")
};

// 服装资产配置
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoClothingPakConfig : public UNeoPakConfigAssetBase
{
    GENERATED_BODY()

public:
    // 依赖的骨骼资产DataAsset
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dependencies", meta = (AllowedClasses = "NeoSkeletonPakConfig"))
    TSoftObjectPtr<UNeoSkeletonPakConfig> RequiredSkeletonConfig;
    
    // 要打包的服装网格
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing Asset")
    TSoftObjectPtr<USkeletalMesh> ClothingMesh;
    
    // 服装类型分类
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Options")
    EClothingType ClothingType = EClothingType::Armor;

#if WITH_EDITOR
    virtual bool ValidateConfiguration() override;
    virtual bool ExecutePackaging() override;
    virtual TArray<FSoftObjectPath> GetAssetsToPackage() const override;
#endif
};
