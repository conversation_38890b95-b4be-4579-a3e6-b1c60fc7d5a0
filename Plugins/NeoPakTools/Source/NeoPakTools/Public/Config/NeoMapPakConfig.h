// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "Engine/World.h"
#include "NeoMapPakConfig.generated.h"

// 地图资产配置
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoMapPakConfig : public UNeoPakConfigAssetBase
{
    GENERATED_BODY()

public:
    // 要打包的地图
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Asset")
    TSoftObjectPtr<UWorld> Map;

#if WITH_EDITOR
    virtual bool ValidateConfiguration() override;
    virtual bool ExecutePackaging() override;
    virtual TArray<FSoftObjectPath> GetAssetsToPackage() const override;
#endif
};
