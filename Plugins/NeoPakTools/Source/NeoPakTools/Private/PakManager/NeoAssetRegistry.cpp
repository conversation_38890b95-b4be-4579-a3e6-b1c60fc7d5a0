// Copyright Epic Games, Inc. All Rights Reserved.

#include "PakManager/NeoAssetRegistry.h"
#include "NeoPakTools.h"
#include "Misc/DateTime.h"

DEFINE_LOG_CATEGORY(LogNeoAssetRegistry);

UNeoAssetRegistry::UNeoAssetRegistry()
{
}

bool UNeoAssetRegistry::RegisterLoadedAsset(const FSoftObjectPath& AssetPath, const FString& PakFilePath, const FString& AssetClass)
{
    FString AssetPathString = AssetPath.ToString();
    
    // 创建资产信息
    FNeoAssetInfo AssetInfo(AssetPath, PakFilePath, AssetClass);
    AssetInfo.bIsLoaded = true;
    AssetInfo.LoadTime = FDateTime::Now();
    
    // 添加到映射中
    AssetInfoMap.Add(AssetPathString, AssetInfo);
    
    // 更新映射关系
    UpdateMappings(AssetPathString, PakFilePath, AssetClass, true);
    
    UE_LOG(LogNeoAssetRegistry, Log, TEXT("Registered asset: %s from PAK: %s"), *AssetPathString, *PakFilePath);
    return true;
}

bool UNeoAssetRegistry::UnregisterAsset(const FSoftObjectPath& AssetPath)
{
    FString AssetPathString = AssetPath.ToString();
    
    if (FNeoAssetInfo* AssetInfo = AssetInfoMap.Find(AssetPathString))
    {
        // 更新映射关系
        UpdateMappings(AssetPathString, AssetInfo->PakFilePath, AssetInfo->AssetClass, false);
        
        // 从映射中移除
        AssetInfoMap.Remove(AssetPathString);
        
        UE_LOG(LogNeoAssetRegistry, Log, TEXT("Unregistered asset: %s"), *AssetPathString);
        return true;
    }
    
    return false;
}

FNeoAssetQueryResult UNeoAssetRegistry::FindAssetsByPak(const FString& PakFilePath)
{
    FNeoAssetQueryResult Result;
    double StartTime = FPlatformTime::Seconds();
    
    if (TArray<FString>* AssetPaths = PakToAssetsMap.Find(PakFilePath))
    {
        for (const FString& AssetPath : *AssetPaths)
        {
            if (FNeoAssetInfo* AssetInfo = AssetInfoMap.Find(AssetPath))
            {
                Result.Assets.Add(*AssetInfo);
            }
        }
        Result.bSuccess = true;
    }
    else
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("No assets found for PAK: %s"), *PakFilePath);
    }
    
    double EndTime = FPlatformTime::Seconds();
    Result.QueryTimeMs = (EndTime - StartTime) * 1000.0f;
    
    return Result;
}

FNeoAssetQueryResult UNeoAssetRegistry::FindAssetsByClass(const FString& AssetClass)
{
    FNeoAssetQueryResult Result;
    double StartTime = FPlatformTime::Seconds();
    
    if (TArray<FString>* AssetPaths = ClassToAssetsMap.Find(AssetClass))
    {
        for (const FString& AssetPath : *AssetPaths)
        {
            if (FNeoAssetInfo* AssetInfo = AssetInfoMap.Find(AssetPath))
            {
                Result.Assets.Add(*AssetInfo);
            }
        }
        Result.bSuccess = true;
    }
    else
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("No assets found for class: %s"), *AssetClass);
    }
    
    double EndTime = FPlatformTime::Seconds();
    Result.QueryTimeMs = (EndTime - StartTime) * 1000.0f;
    
    return Result;
}

FNeoAssetQueryResult UNeoAssetRegistry::FindAssetsByPath(const FString& PathPattern)
{
    FNeoAssetQueryResult Result;
    double StartTime = FPlatformTime::Seconds();
    
    for (const auto& Pair : AssetInfoMap)
    {
        if (MatchesPathPattern(Pair.Key, PathPattern))
        {
            Result.Assets.Add(Pair.Value);
        }
    }
    
    Result.bSuccess = true;
    
    double EndTime = FPlatformTime::Seconds();
    Result.QueryTimeMs = (EndTime - StartTime) * 1000.0f;
    
    return Result;
}

bool UNeoAssetRegistry::GetAssetInfo(const FSoftObjectPath& AssetPath, FNeoAssetInfo& OutAssetInfo)
{
    FString AssetPathString = AssetPath.ToString();
    
    if (FNeoAssetInfo* AssetInfo = AssetInfoMap.Find(AssetPathString))
    {
        OutAssetInfo = *AssetInfo;
        return true;
    }
    
    return false;
}

bool UNeoAssetRegistry::RegisterPakFile(const FString& PakFilePath)
{
    FNeoPakInfo PakInfo(PakFilePath);
    PakInfo.bIsMounted = true;
    PakInfo.MountTime = FDateTime::Now();
    
    PakInfoMap.Add(PakFilePath, PakInfo);
    
    UE_LOG(LogNeoAssetRegistry, Log, TEXT("Registered PAK file: %s"), *PakFilePath);
    return true;
}

bool UNeoAssetRegistry::UnregisterPakFile(const FString& PakFilePath)
{
    if (PakInfoMap.Remove(PakFilePath) > 0)
    {
        // 同时移除该PAK的所有资产
        if (TArray<FString>* AssetPaths = PakToAssetsMap.Find(PakFilePath))
        {
            for (const FString& AssetPath : *AssetPaths)
            {
                AssetInfoMap.Remove(AssetPath);
            }
        }
        PakToAssetsMap.Remove(PakFilePath);
        
        UE_LOG(LogNeoAssetRegistry, Log, TEXT("Unregistered PAK file: %s"), *PakFilePath);
        return true;
    }
    
    return false;
}

bool UNeoAssetRegistry::GetPakInfo(const FString& PakFilePath, FNeoPakInfo& OutPakInfo)
{
    if (FNeoPakInfo* PakInfo = PakInfoMap.Find(PakFilePath))
    {
        OutPakInfo = *PakInfo;
        return true;
    }
    
    return false;
}

TArray<FString> UNeoAssetRegistry::GetAllRegisteredPaks() const
{
    TArray<FString> PakFiles;
    PakInfoMap.GetKeys(PakFiles);
    return PakFiles;
}

FNeoAssetQueryResult UNeoAssetRegistry::GetAllRegisteredAssets()
{
    FNeoAssetQueryResult Result;
    double StartTime = FPlatformTime::Seconds();
    
    for (const auto& Pair : AssetInfoMap)
    {
        Result.Assets.Add(Pair.Value);
    }
    
    Result.bSuccess = true;
    
    double EndTime = FPlatformTime::Seconds();
    Result.QueryTimeMs = (EndTime - StartTime) * 1000.0f;
    
    return Result;
}

bool UNeoAssetRegistry::UpdateAssetLoadStatus(const FSoftObjectPath& AssetPath, bool bIsLoaded)
{
    FString AssetPathString = AssetPath.ToString();
    
    if (FNeoAssetInfo* AssetInfo = AssetInfoMap.Find(AssetPathString))
    {
        AssetInfo->bIsLoaded = bIsLoaded;
        if (bIsLoaded)
        {
            AssetInfo->LoadTime = FDateTime::Now();
        }
        return true;
    }
    
    return false;
}

int32 UNeoAssetRegistry::AddAssetReference(const FSoftObjectPath& AssetPath)
{
    FString AssetPathString = AssetPath.ToString();
    
    if (FNeoAssetInfo* AssetInfo = AssetInfoMap.Find(AssetPathString))
    {
        AssetInfo->ReferenceCount++;
        return AssetInfo->ReferenceCount;
    }
    
    return 0;
}

int32 UNeoAssetRegistry::RemoveAssetReference(const FSoftObjectPath& AssetPath)
{
    FString AssetPathString = AssetPath.ToString();
    
    if (FNeoAssetInfo* AssetInfo = AssetInfoMap.Find(AssetPathString))
    {
        AssetInfo->ReferenceCount = FMath::Max(0, AssetInfo->ReferenceCount - 1);
        return AssetInfo->ReferenceCount;
    }
    
    return 0;
}

void UNeoAssetRegistry::ClearAllRegistrations()
{
    AssetInfoMap.Empty();
    PakInfoMap.Empty();
    PakToAssetsMap.Empty();
    ClassToAssetsMap.Empty();
    
    UE_LOG(LogNeoAssetRegistry, Log, TEXT("Cleared all registrations"));
}

void UNeoAssetRegistry::GetRegistryStatistics(int32& OutTotalAssets, int32& OutLoadedAssets, int32& OutTotalPaks) const
{
    OutTotalAssets = AssetInfoMap.Num();
    OutTotalPaks = PakInfoMap.Num();
    
    OutLoadedAssets = 0;
    for (const auto& Pair : AssetInfoMap)
    {
        if (Pair.Value.bIsLoaded)
        {
            OutLoadedAssets++;
        }
    }
}

void UNeoAssetRegistry::UpdateMappings(const FString& AssetPath, const FString& PakFilePath, const FString& AssetClass, bool bAdd)
{
    if (bAdd)
    {
        // 添加到PAK映射
        TArray<FString>& PakAssets = PakToAssetsMap.FindOrAdd(PakFilePath);
        PakAssets.AddUnique(AssetPath);
        
        // 添加到类型映射
        TArray<FString>& ClassAssets = ClassToAssetsMap.FindOrAdd(AssetClass);
        ClassAssets.AddUnique(AssetPath);
    }
    else
    {
        // 从PAK映射中移除
        if (TArray<FString>* PakAssets = PakToAssetsMap.Find(PakFilePath))
        {
            PakAssets->Remove(AssetPath);
            if (PakAssets->Num() == 0)
            {
                PakToAssetsMap.Remove(PakFilePath);
            }
        }
        
        // 从类型映射中移除
        if (TArray<FString>* ClassAssets = ClassToAssetsMap.Find(AssetClass))
        {
            ClassAssets->Remove(AssetPath);
            if (ClassAssets->Num() == 0)
            {
                ClassToAssetsMap.Remove(AssetClass);
            }
        }
    }
}

bool UNeoAssetRegistry::MatchesPathPattern(const FString& AssetPath, const FString& Pattern) const
{
    // 简单的通配符匹配实现
    if (Pattern.Contains(TEXT("*")))
    {
        // 支持简单的通配符匹配
        FString PatternRegex = Pattern;
        PatternRegex = PatternRegex.Replace(TEXT("*"), TEXT(".*"));
        
        // 这里应该使用正则表达式，但为了简化，我们使用简单的包含检查
        FString PatternWithoutWildcard = Pattern;
        PatternWithoutWildcard = PatternWithoutWildcard.Replace(TEXT("*"), TEXT(""));
        
        return AssetPath.Contains(PatternWithoutWildcard);
    }
    else
    {
        return AssetPath.Contains(Pattern);
    }
}
