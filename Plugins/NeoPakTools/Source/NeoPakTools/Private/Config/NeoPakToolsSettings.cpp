// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoPakToolsSettings.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"

#define LOCTEXT_NAMESPACE "NeoPakToolsSettings"

UNeoPakToolsSettings::UNeoPakToolsSettings()
{
    // 设置默认值
    DefaultOutputDirectory.Path = FPaths::ProjectDir() / TEXT("Paks");
    bUseRelativePaths = true;
    bAutoCreateOutputDirectory = true;
    
    // 设置默认压缩设置
    DefaultCompressionSettings.bEnableCompression = true;
    DefaultCompressionSettings.CompressionLevel = 6;
    DefaultCompressionSettings.CompressionMethod = EPakCompressionMethod::Zlib;
    DefaultCompressionSettings.CompressionBlockSize = 256;
    DefaultCompressionSettings.MinCompressionPercentSaved = 5;

    // 设置默认加密设置
    DefaultEncryptionSettings.bEnableEncryption = false;
    DefaultEncryptionSettings.EncryptionKey = TEXT("");
    DefaultEncryptionSettings.bEncryptPakIndex = true;
    DefaultEncryptionSettings.bEncryptIniFiles = true;
    DefaultEncryptionSettings.bEncryptUAssetFiles = false;
    DefaultEncryptionSettings.bEncryptAllFiles = false;
}

FString UNeoPakToolsSettings::GetFullOutputPath(const FString& FileName) const
{
    FString OutputPath = DefaultOutputDirectory.Path;
    
    // 如果使用相对路径，则相对于项目目录
    if (bUseRelativePaths && !FPaths::IsRelative(OutputPath))
    {
        OutputPath = FPaths::ProjectDir() / OutputPath;
    }
    
    // 确保路径是绝对路径
    OutputPath = FPaths::ConvertRelativePathToFull(OutputPath);
    
    // 组合文件名
    if (!FileName.IsEmpty())
    {
        OutputPath = FPaths::Combine(OutputPath, FileName);
    }
    
    return OutputPath;
}

UNeoPakToolsSettings* UNeoPakToolsSettings::GetNeoPakToolsSettings()
{
    return GetMutableDefault<UNeoPakToolsSettings>();
}

#if WITH_EDITOR
FText UNeoPakToolsSettings::GetSectionText() const
{
    return LOCTEXT("NeoPakToolsSettingsSection", "NeoPakTools");
}

FText UNeoPakToolsSettings::GetSectionDescription() const
{
    return LOCTEXT("NeoPakToolsSettingsDescription", "Configure NeoPakTools plugin settings");
}
#endif

#undef LOCTEXT_NAMESPACE
