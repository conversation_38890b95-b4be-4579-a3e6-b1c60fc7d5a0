// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoPakFileCreator.h"
#include "NeoPakTools.h"
#include "Config/NeoPakToolsSettings.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformProcess.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetRegistry/IAssetRegistry.h"
#include "Engine/Engine.h"
#include "Misc/PackageName.h"

bool FNeoPakFileCreator::CreatePakFromConfig(UNeoPakConfigAssetBase* ConfigAsset)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    // Get assets to package
    TArray<FSoftObjectPath> SoftAssetPaths = ConfigAsset->GetAssetsToPackage();
    TArray<FString> AssetPaths;
    
    for (const FSoftObjectPath& SoftPath : SoftAssetPaths)
    {
        AssetPaths.Add(SoftPath.ToString());
    }

    if (AssetPaths.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No assets to package for config: %s. Please ensure you have set the required asset(s) in the configuration."), *ConfigAsset->ConfigName);
        return false;
    }

    // Get output path
    FString OutputPath = ConfigAsset->GetFullOutputPath();

    return CreatePakFromAssets(AssetPaths, OutputPath);
}

bool FNeoPakFileCreator::CreatePakFromAssets(const TArray<FString>& AssetPaths, const FString& OutputPath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating PAK file: %s"), *OutputPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Assets to package: %d"), AssetPaths.Num());

    // Validate UnrealPak availability
    if (!IsUnrealPakAvailable())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak is not available"));
        return false;
    }

    // Validate UnrealEditor-Cmd availability
    if (!IsUnrealEditorCmdAvailable())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealEditor-Cmd is not available"));
        return false;
    }

    // Validate asset paths
    TArray<FString> MissingAssets;
    if (!ValidateAssetPaths(AssetPaths, MissingAssets))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Some assets are missing:"));
        for (const FString& MissingAsset : MissingAssets)
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("  - %s"), *MissingAsset);
        }
        return false;
    }

    // Step 1: Cook assets first
    UE_LOG(LogNeoPakTools, Log, TEXT("Step 1: Cooking assets..."));
    if (!CookAssets(AssetPaths, TEXT("Windows")))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to cook assets"));
        return false;
    }

    // Ensure output directory exists
    FString OutputDirectory = FPaths::GetPath(OutputPath);
    if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*OutputDirectory))
    {
        if (!FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*OutputDirectory))
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create output directory: %s"), *OutputDirectory);
            return false;
        }
    }

    // Step 2: Create response file using cooked assets
    UE_LOG(LogNeoPakTools, Log, TEXT("Step 2: Creating PAK from cooked assets..."));
    FString ResponseFilePath = GenerateResponseFilePath(FPaths::GetBaseFilename(OutputPath));
    if (!CreateCookedResponseFile(AssetPaths, ResponseFilePath, TEXT("Windows")))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create cooked response file: %s"), *ResponseFilePath);
        return false;
    }

    // Step 3: Execute UnrealPak with advanced options
    FString AdditionalArgs = TEXT("-compressionformats=Oodle -compresslevel=4 -compressmethod=Kraken -platform=Windows");
    bool bSuccess = ExecuteUnrealPak(OutputPath, ResponseFilePath, AdditionalArgs);

    // Clean up temporary files
    CleanupTempFiles(ResponseFilePath);

    if (bSuccess)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully created PAK file: %s"), *OutputPath);
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create PAK file: %s"), *OutputPath);
    }

    return bSuccess;
}

FString FNeoPakFileCreator::GetUnrealPakPath()
{
    // Try to find UnrealPak in the engine binaries directory
    FString EngineBinariesPath = FPaths::EngineDir() / TEXT("Binaries");

#if PLATFORM_WINDOWS
    FString UnrealPakPath = EngineBinariesPath / TEXT("Win64") / TEXT("UnrealPak.exe");
#elif PLATFORM_MAC
    FString UnrealPakPath = EngineBinariesPath / TEXT("Mac") / TEXT("UnrealPak");
#elif PLATFORM_LINUX
    FString UnrealPakPath = EngineBinariesPath / TEXT("Linux") / TEXT("UnrealPak");
#else
    FString UnrealPakPath = TEXT("");
#endif

    return UnrealPakPath;
}

bool FNeoPakFileCreator::IsUnrealPakAvailable()
{
    FString UnrealPakPath = GetUnrealPakPath();
    return !UnrealPakPath.IsEmpty() && FPlatformFileManager::Get().GetPlatformFile().FileExists(*UnrealPakPath);
}

FString FNeoPakFileCreator::GetUnrealEditorCmdPath()
{
    // Try to find UnrealEditor-Cmd in the engine binaries directory
    FString EngineBinariesPath = FPaths::EngineDir() / TEXT("Binaries");

#if PLATFORM_WINDOWS
    FString UnrealEditorCmdPath = EngineBinariesPath / TEXT("Win64") / TEXT("UnrealEditor-Cmd.exe");
#elif PLATFORM_MAC
    FString UnrealEditorCmdPath = EngineBinariesPath / TEXT("Mac") / TEXT("UnrealEditor-Cmd");
#elif PLATFORM_LINUX
    FString UnrealEditorCmdPath = EngineBinariesPath / TEXT("Linux") / TEXT("UnrealEditor-Cmd");
#else
    FString UnrealEditorCmdPath = TEXT("");
#endif

    return UnrealEditorCmdPath;
}

bool FNeoPakFileCreator::IsUnrealEditorCmdAvailable()
{
    FString UnrealEditorCmdPath = GetUnrealEditorCmdPath();
    return !UnrealEditorCmdPath.IsEmpty() && FPlatformFileManager::Get().GetPlatformFile().FileExists(*UnrealEditorCmdPath);
}

bool FNeoPakFileCreator::CreateResponseFile(const TArray<FString>& AssetPaths, const FString& ResponseFilePath)
{
    TArray<FString> ResponseLines;
    TArray<FString> FilePaths = ConvertAssetPathsToFilePaths(AssetPaths);

    UE_LOG(LogNeoPakTools, Log, TEXT("Creating response file with %d asset paths"), AssetPaths.Num());

    for (int32 i = 0; i < AssetPaths.Num() && i < FilePaths.Num(); ++i)
    {
        const FString& AssetPath = AssetPaths[i];
        const FString& FilePath = FilePaths[i];

        UE_LOG(LogNeoPakTools, Log, TEXT("Processing asset %d: %s -> %s"), i, *AssetPath, *FilePath);

        if (!FilePath.IsEmpty())
        {
            // 检查源文件是否存在
            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
            {
                // 将资产路径转换为PAK内的路径格式
                FString PakInternalPath = AssetPath;
                if (PakInternalPath.StartsWith(TEXT("/Game/")))
                {
                    PakInternalPath = PakInternalPath.RightChop(6); // 移除"/Game/"前缀
                }

                // Format: "SourceFile" "DestinationPath"
                FString ResponseLine = FString::Printf(TEXT("\"%s\" \"%s\""), *FilePath, *PakInternalPath);
                ResponseLines.Add(ResponseLine);
                UE_LOG(LogNeoPakTools, Log, TEXT("Added to response file: %s"), *ResponseLine);
            }
            else
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("Source file does not exist: %s"), *FilePath);
            }
        }
        else
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Empty file path for asset: %s"), *AssetPath);
        }
    }

    if (ResponseLines.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No valid file paths found for response file"));
        return false;
    }

    FString ResponseContent = FString::Join(ResponseLines, TEXT("\n"));

    UE_LOG(LogNeoPakTools, Log, TEXT("Response file content:\n%s"), *ResponseContent);

    if (!FFileHelper::SaveStringToFile(ResponseContent, *ResponseFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to save response file: %s"), *ResponseFilePath);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Created response file: %s with %d entries"), *ResponseFilePath, ResponseLines.Num());
    return true;
}

bool FNeoPakFileCreator::ExecuteUnrealPak(const FString& PakFilePath, const FString& ResponseFilePath, const FString& AdditionalArgs)
{
    FString UnrealPakPath = GetUnrealPakPath();
    if (UnrealPakPath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak executable not found"));
        return false;
    }

    // Build command line arguments
    FString Arguments = FString::Printf(TEXT("\"%s\" -create=\"%s\" %s"), *PakFilePath, *ResponseFilePath, *AdditionalArgs);

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing UnrealPak: %s %s"), *UnrealPakPath, *Arguments);

    // Execute UnrealPak
    int32 ReturnCode = -1;
    FString StdOut;
    FString StdErr;

    bool bSuccess = FPlatformProcess::ExecProcess(*UnrealPakPath, *Arguments, &ReturnCode, &StdOut, &StdErr);

    if (bSuccess && ReturnCode == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("UnrealPak completed successfully"));
        if (!StdOut.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("UnrealPak output: %s"), *StdOut);
        }
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak failed with return code: %d"), ReturnCode);
        if (!StdErr.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak error: %s"), *StdErr);
        }
        if (!StdOut.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("UnrealPak output: %s"), *StdOut);
        }
        return false;
    }
}

TArray<FString> FNeoPakFileCreator::ConvertAssetPathsToFilePaths(const TArray<FString>& AssetPaths)
{
    TArray<FString> FilePaths;
    FString ContentDir = GetContentDirectory();

    UE_LOG(LogNeoPakTools, Log, TEXT("Converting asset paths to file paths. Content directory: %s"), *ContentDir);

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Converting asset path: %s"), *AssetPath);

        FString FilePath;
        bool bConversionSuccessful = false;

        // First try our alternative conversion method (better for maps and special cases)
        FString AlternativeFilePath = ConvertAssetPathAlternative(AssetPath);
        if (!AlternativeFilePath.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Alternative conversion successful: %s"), *AlternativeFilePath);

            // 验证文件是否真的存在
            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*AlternativeFilePath))
            {
                UE_LOG(LogNeoPakTools, Log, TEXT("  File exists, using alternative path"));
                FilePaths.Add(AlternativeFilePath);
                bConversionSuccessful = true;
            }
            else
            {
                UE_LOG(LogNeoPakTools, Warning, TEXT("  Alternative path does not exist: %s"), *AlternativeFilePath);
            }
        }

        if (!bConversionSuccessful)
        {
            // Fallback to standard conversion
            if (FPackageName::TryConvertLongPackageNameToFilename(AssetPath, FilePath))
            {
                // Convert to absolute path if it's relative
                if (FPaths::IsRelative(FilePath))
                {
                    FilePath = FPaths::ConvertRelativePathToFull(FilePath);
                }

                UE_LOG(LogNeoPakTools, Log, TEXT("  Standard conversion result: %s"), *FilePath);

                // 验证标准转换的文件是否存在
                if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
                {
                    UE_LOG(LogNeoPakTools, Log, TEXT("  Standard conversion file exists"));
                    FilePaths.Add(FilePath);
                    bConversionSuccessful = true;
                }
                else
                {
                    UE_LOG(LogNeoPakTools, Warning, TEXT("  Standard conversion file does not exist: %s"), *FilePath);
                }
            }
            else
            {
                UE_LOG(LogNeoPakTools, Warning, TEXT("  Standard conversion failed for: %s"), *AssetPath);
            }
        }

        if (!bConversionSuccessful)
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("  Failed to convert asset path to file path: %s"), *AssetPath);
            FilePaths.Add(TEXT(""));
        }
    }

    return FilePaths;
}

FString FNeoPakFileCreator::GetContentDirectory()
{
    FString ContentDir = FPaths::ProjectContentDir();
    // Ensure it's an absolute path
    ContentDir = FPaths::ConvertRelativePathToFull(ContentDir);
    // Normalize path separators
    FPaths::NormalizeDirectoryName(ContentDir);
    return ContentDir;
}

bool FNeoPakFileCreator::ValidateAssetPaths(const TArray<FString>& AssetPaths, TArray<FString>& OutMissingAssets)
{
    OutMissingAssets.Empty();

    UE_LOG(LogNeoPakTools, Log, TEXT("Validating %d asset paths"), AssetPaths.Num());

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Validating asset path: %s"), *AssetPath);

        // Check if the asset path is valid format
        if (AssetPath.IsEmpty() || !AssetPath.StartsWith(TEXT("/Game/")))
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("  Invalid asset path format: %s"), *AssetPath);
            OutMissingAssets.Add(AssetPath);
            continue;
        }

        // Try to validate using Asset Registry first (more reliable for UE assets)
        if (ValidateAssetUsingRegistry(AssetPath))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Asset found in registry: %s"), *AssetPath);
            continue;
        }

        // For now, if asset registry doesn't find it, we'll still try to package it
        // UnrealPak will give us the final word on whether the asset exists
        UE_LOG(LogNeoPakTools, Warning, TEXT("  Asset not found in registry, but will attempt to package: %s"), *AssetPath);
    }

    // For now, we'll be more permissive and let UnrealPak handle missing assets
    // This prevents false positives where assets exist but aren't found by our validation
    UE_LOG(LogNeoPakTools, Log, TEXT("Asset validation completed. Found %d potentially missing assets, but proceeding with packaging."), OutMissingAssets.Num());

    // Clear missing assets for now - let UnrealPak be the final judge
    OutMissingAssets.Empty();
    return true;
}

FString FNeoPakFileCreator::GenerateResponseFilePath(const FString& PakFileName)
{
    FString TempDir = FPaths::ProjectIntermediateDir() / TEXT("NeoPakTools");
    FString ResponseFileName = PakFileName + TEXT("_response.txt");
    return TempDir / ResponseFileName;
}

void FNeoPakFileCreator::CleanupTempFiles(const FString& ResponseFilePath)
{
    if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*ResponseFilePath))
    {
        FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*ResponseFilePath);
        UE_LOG(LogNeoPakTools, Log, TEXT("Cleaned up response file: %s"), *ResponseFilePath);
    }
}

bool FNeoPakFileCreator::ValidateAssetUsingRegistry(const FString& AssetPath)
{
    // Use Asset Registry to check if asset exists
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Convert the asset path to FSoftObjectPath for registry lookup
    FSoftObjectPath SoftPath(AssetPath);
    FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(SoftPath);

    if (AssetData.IsValid())
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Asset found in registry: %s (Class: %s)"), *AssetPath, *AssetData.AssetClassPath.ToString());
        return true;
    }

    UE_LOG(LogNeoPakTools, Warning, TEXT("Asset not found in registry: %s"), *AssetPath);
    return false;
}

FString FNeoPakFileCreator::ConvertAssetPathAlternative(const FString& AssetPath)
{
    // Handle special cases where standard conversion fails
    FString ContentDir = GetContentDirectory();

    // 确保Content目录路径是绝对路径且格式正确
    ContentDir = FPaths::ConvertRelativePathToFull(ContentDir);
    FPaths::NormalizeFilename(ContentDir);

    UE_LOG(LogNeoPakTools, Log, TEXT("Alternative conversion for: %s"), *AssetPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Content directory: %s"), *ContentDir);

    // Remove /Game/ prefix
    FString RelativePath = AssetPath;
    if (RelativePath.StartsWith(TEXT("/Game/")))
    {
        RelativePath = RelativePath.RightChop(6); // Remove "/Game/"
        UE_LOG(LogNeoPakTools, Log, TEXT("Removed /Game/ prefix, relative path: %s"), *RelativePath);
    }

    // Handle special case for assets with duplicate names (like Maps/NewMap.NewMap)
    // Remove the duplicate part after the dot
    int32 DotIndex = RelativePath.Find(TEXT("."), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
    if (DotIndex != INDEX_NONE)
    {
        FString BaseName = RelativePath.Left(DotIndex);
        FString Extension = RelativePath.RightChop(DotIndex + 1);

        // Check if the extension matches the base name (like NewMap.NewMap)
        FString BaseFileName = FPaths::GetBaseFilename(BaseName);
        if (Extension == BaseFileName)
        {
            // This is likely a map or similar asset, use just the base path
            RelativePath = BaseName;
            UE_LOG(LogNeoPakTools, Log, TEXT("Detected duplicate name format, using base path: %s"), *RelativePath);
        }
    }

    // Try different file extensions
    TArray<FString> PossibleExtensions = {TEXT(".umap"), TEXT(".uasset"), TEXT(".ubulk")};

    for (const FString& Extension : PossibleExtensions)
    {
        // 使用正确的路径组合方式，避免相对路径问题
        FString TestPath = ContentDir / RelativePath + Extension;
        // 规范化路径，移除任何相对路径符号
        FPaths::NormalizeFilename(TestPath);

        UE_LOG(LogNeoPakTools, Log, TEXT("Testing path: %s"), *TestPath);

        if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*TestPath))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Found alternative file path: %s"), *TestPath);
            return TestPath;
        }
    }

    UE_LOG(LogNeoPakTools, Warning, TEXT("No alternative file path found for: %s"), *AssetPath);
    return TEXT("");
}

bool FNeoPakFileCreator::CookAssets(const TArray<FString>& AssetPaths, const FString& TargetPlatform)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Cooking %d assets for platform: %s"), AssetPaths.Num(), *TargetPlatform);

    // Get project file path
    FString ProjectFilePath = FPaths::GetProjectFilePath();
    if (ProjectFilePath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Could not find project file"));
        return false;
    }

    return ExecuteCookCommand(AssetPaths, TargetPlatform);
}

bool FNeoPakFileCreator::CookDirectory(const FString& DirectoryPath, const FString& TargetPlatform)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Cooking directory: %s for platform: %s"), *DirectoryPath, *TargetPlatform);

    FString UnrealEditorCmdPath = GetUnrealEditorCmdPath();
    if (UnrealEditorCmdPath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealEditor-Cmd executable not found"));
        return false;
    }

    FString ProjectFilePath = FPaths::GetProjectFilePath();

    // Convert directory path to proper format
    FString CookDirPath = DirectoryPath;
    if (!CookDirPath.StartsWith(TEXT("/Game/")))
    {
        if (CookDirPath.StartsWith(TEXT("/")))
        {
            CookDirPath = TEXT("/Game") + CookDirPath;
        }
        else
        {
            CookDirPath = TEXT("/Game/") + CookDirPath;
        }
    }

    // Build cook command for directory (corrected format based on Cook代码分析.md)
    FString Arguments = FString::Printf(
        TEXT("\"%s\" -run=Cook -TargetPlatform=%s -CookDir=%s -unversioned -stdout -unattended"),
        *ProjectFilePath,
        *TargetPlatform,
        *CookDirPath
    );

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing Cook directory command: %s %s"), *UnrealEditorCmdPath, *Arguments);

    // Execute Cook command
    int32 ReturnCode = -1;
    FString StdOut;
    FString StdErr;

    bool bSuccess = FPlatformProcess::ExecProcess(*UnrealEditorCmdPath, *Arguments, &ReturnCode, &StdOut, &StdErr);

    if (bSuccess && ReturnCode == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Cook directory completed successfully"));
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Cook directory failed with return code: %d"), ReturnCode);
        if (!StdErr.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Cook error: %s"), *StdErr);
        }
        return false;
    }
}

bool FNeoPakFileCreator::CookMaps(const TArray<FString>& MapNames, const FString& TargetPlatform)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Cooking %d maps for platform: %s"), MapNames.Num(), *TargetPlatform);

    FString UnrealEditorCmdPath = GetUnrealEditorCmdPath();
    if (UnrealEditorCmdPath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealEditor-Cmd executable not found"));
        return false;
    }

    FString ProjectFilePath = FPaths::GetProjectFilePath();

    // Build cook command for maps
    FString Arguments = FString::Printf(
        TEXT("\"%s\" -run=Cook -TargetPlatform=%s -unversioned -fileopenlog -stdout -CrashForUAT -unattended -NoLogTimes"),
        *ProjectFilePath,
        *TargetPlatform
    );

    // Add each map
    for (const FString& MapName : MapNames)
    {
        FString CleanMapName = FPaths::GetBaseFilename(MapName);
        Arguments += FString::Printf(TEXT(" -Map=%s"), *CleanMapName);
        UE_LOG(LogNeoPakTools, Log, TEXT("Added map to cook: %s"), *CleanMapName);
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing Cook maps command: %s %s"), *UnrealEditorCmdPath, *Arguments);

    // Execute Cook command
    int32 ReturnCode = -1;
    FString StdOut;
    FString StdErr;

    bool bSuccess = FPlatformProcess::ExecProcess(*UnrealEditorCmdPath, *Arguments, &ReturnCode, &StdOut, &StdErr);

    if (bSuccess && ReturnCode == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Cook maps completed successfully"));
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Cook maps failed with return code: %d"), ReturnCode);
        if (!StdErr.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Cook error: %s"), *StdErr);
        }
        return false;
    }
}

bool FNeoPakFileCreator::ExecuteCookCommand(const TArray<FString>& AssetPaths, const FString& TargetPlatform)
{
    FString UnrealEditorCmdPath = GetUnrealEditorCmdPath();
    if (UnrealEditorCmdPath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealEditor-Cmd executable not found"));
        return false;
    }

    FString ProjectFilePath = FPaths::GetProjectFilePath();

    // Build cook command arguments with specific asset paths (corrected format)
    FString Arguments = FString::Printf(
        TEXT("\"%s\" -run=Cook -TargetPlatform=%s -stdout -unattended"),
        *ProjectFilePath,
        *TargetPlatform
    );

    // Add specific assets/directories to cook
    if (AssetPaths.Num() > 0)
    {
        // Group assets by type for more efficient cooking
        TArray<FString> MapPaths;
        TArray<FString> DirectoryPaths;
        TArray<FString> PackagePaths;

        for (const FString& AssetPath : AssetPaths)
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Processing asset path for Cook: %s"), *AssetPath);

            if (AssetPath.StartsWith(TEXT("/Game/")))
            {
                // Convert /Game/ paths to appropriate Cook parameters
                FString RelativePath = AssetPath.RightChop(6); // Remove "/Game/"

                // Check if it's a map
                if (RelativePath.Contains(TEXT("Maps/")) || AssetPath.EndsWith(TEXT(".umap")))
                {
                    // Extract map name for -Map parameter
                    FString MapName = FPaths::GetBaseFilename(RelativePath);
                    if (MapName.Contains(TEXT(".")))
                    {
                        // Handle cases like "TestMap1.TestMap1"
                        MapName = MapName.Left(MapName.Find(TEXT(".")));
                    }
                    MapPaths.AddUnique(MapName);
                    UE_LOG(LogNeoPakTools, Log, TEXT("Added map for cooking: %s"), *MapName);
                }
                else if (RelativePath.EndsWith(TEXT("/")))
                {
                    // It's a directory path
                    DirectoryPaths.AddUnique(RelativePath);
                    UE_LOG(LogNeoPakTools, Log, TEXT("Added directory for cooking: %s"), *RelativePath);
                }
                else
                {
                    // It's a specific package/asset
                    PackagePaths.AddUnique(RelativePath);
                    UE_LOG(LogNeoPakTools, Log, TEXT("Added package for cooking: %s"), *RelativePath);
                }
            }
        }

        // Add map parameters
        for (const FString& MapPath : MapPaths)
        {
            Arguments += FString::Printf(TEXT(" -Map=%s"), *MapPath);
        }

        // Add directory parameters
        for (const FString& DirPath : DirectoryPaths)
        {
            Arguments += FString::Printf(TEXT(" -CookDir=/Game/%s"), *DirPath);
        }

        // Add package parameters
        for (const FString& PackagePath : PackagePaths)
        {
            Arguments += FString::Printf(TEXT(" -Package=/Game/%s"), *PackagePath);
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing Cook command: %s %s"), *UnrealEditorCmdPath, *Arguments);

    // Execute Cook command
    int32 ReturnCode = -1;
    FString StdOut;
    FString StdErr;

    bool bSuccess = FPlatformProcess::ExecProcess(*UnrealEditorCmdPath, *Arguments, &ReturnCode, &StdOut, &StdErr);

    if (bSuccess && ReturnCode == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Cook completed successfully"));
        if (!StdOut.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Cook output: %s"), *StdOut);
        }
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Cook failed with return code: %d"), ReturnCode);
        if (!StdErr.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Cook error: %s"), *StdErr);
        }
        if (!StdOut.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Cook output: %s"), *StdOut);
        }
        return false;
    }
}

FString FNeoPakFileCreator::GetCookedContentDirectory(const FString& TargetPlatform)
{
    // Get the cooked content directory path: ProjectDir/Saved/Cooked/Platform/ProjectName/Content
    FString ProjectDir = FPaths::ProjectDir();
    FString ProjectName = FApp::GetProjectName();
    FString CookedDir = ProjectDir / TEXT("Saved") / TEXT("Cooked") / TargetPlatform / ProjectName / TEXT("Content");

    // Ensure it's an absolute path
    CookedDir = FPaths::ConvertRelativePathToFull(CookedDir);
    FPaths::NormalizeDirectoryName(CookedDir);

    return CookedDir;
}

TArray<FString> FNeoPakFileCreator::ConvertAssetPathsToCookedFilePaths(const TArray<FString>& AssetPaths, const FString& TargetPlatform)
{
    TArray<FString> CookedFilePaths;
    FString CookedContentDir = GetCookedContentDirectory(TargetPlatform);

    UE_LOG(LogNeoPakTools, Log, TEXT("Converting asset paths to cooked file paths. Cooked directory: %s"), *CookedContentDir);

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Converting cooked asset path: %s"), *AssetPath);

        // Remove /Game/ prefix
        FString RelativePath = AssetPath;
        if (RelativePath.StartsWith(TEXT("/Game/")))
        {
            RelativePath = RelativePath.RightChop(6); // Remove "/Game/"
        }

        // Handle special case for assets with duplicate names (like Maps/TestMap1.TestMap1)
        int32 DotIndex = RelativePath.Find(TEXT("."), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
        if (DotIndex != INDEX_NONE)
        {
            FString BaseName = RelativePath.Left(DotIndex);
            FString Extension = RelativePath.RightChop(DotIndex + 1);

            // Check if the extension matches the base name (like TestMap1.TestMap1)
            FString BaseFileName = FPaths::GetBaseFilename(BaseName);
            if (Extension == BaseFileName)
            {
                RelativePath = BaseName;
            }
        }

        // Try to find cooked files with different strategies
        TArray<FString> FoundFiles;

        // Strategy 1: Direct file lookup with common extensions
        TArray<FString> PossibleExtensions = {TEXT(".umap"), TEXT(".uasset"), TEXT(".uexp"), TEXT(".ubulk")};

        for (const FString& Extension : PossibleExtensions)
        {
            FString TestPath = CookedContentDir / RelativePath + Extension;
            FPaths::NormalizeFilename(TestPath);

            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*TestPath))
            {
                FoundFiles.Add(TestPath);
                UE_LOG(LogNeoPakTools, Log, TEXT("Found cooked file (direct): %s"), *TestPath);
            }
        }

        // Strategy 2: If no direct files found, try directory search
        if (FoundFiles.Num() == 0)
        {
            FString SearchDir = CookedContentDir / FPaths::GetPath(RelativePath);
            FString FileName = FPaths::GetBaseFilename(RelativePath);

            UE_LOG(LogNeoPakTools, Log, TEXT("Searching directory: %s for files matching: %s"), *SearchDir, *FileName);

            if (FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*SearchDir))
            {
                TArray<FString> FilesInDir;
                FPlatformFileManager::Get().GetPlatformFile().FindFiles(FilesInDir, *SearchDir, nullptr);

                for (const FString& FileInDir : FilesInDir)
                {
                    if (FileInDir.Contains(FileName))
                    {
                        FString FullPath = SearchDir / FileInDir;
                        FoundFiles.Add(FullPath);
                        UE_LOG(LogNeoPakTools, Log, TEXT("Found cooked file (search): %s"), *FullPath);
                    }
                }
            }
        }

        // Add all found files
        if (FoundFiles.Num() > 0)
        {
            CookedFilePaths.Append(FoundFiles);
        }
        else
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("No cooked file found for asset: %s"), *AssetPath);
            UE_LOG(LogNeoPakTools, Warning, TEXT("Searched path: %s"), *RelativePath);
            CookedFilePaths.Add(TEXT(""));
        }
    }

    return CookedFilePaths;
}

bool FNeoPakFileCreator::CreateCookedResponseFile(const TArray<FString>& AssetPaths, const FString& ResponseFilePath, const FString& TargetPlatform)
{
    TArray<FString> ResponseLines;
    TArray<FString> CookedFilePaths = ConvertAssetPathsToCookedFilePaths(AssetPaths, TargetPlatform);

    UE_LOG(LogNeoPakTools, Log, TEXT("Creating cooked response file with %d asset paths"), AssetPaths.Num());

    for (int32 i = 0; i < AssetPaths.Num() && i < CookedFilePaths.Num(); ++i)
    {
        const FString& AssetPath = AssetPaths[i];
        const FString& CookedFilePath = CookedFilePaths[i];

        UE_LOG(LogNeoPakTools, Log, TEXT("Processing cooked asset %d: %s -> %s"), i, *AssetPath, *CookedFilePath);

        if (!CookedFilePath.IsEmpty())
        {
            // Check if cooked file exists
            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*CookedFilePath))
            {
                // Convert asset path to PAK internal path format
                FString PakInternalPath = AssetPath;
                if (PakInternalPath.StartsWith(TEXT("/Game/")))
                {
                    FString ProjectName = FApp::GetProjectName();
                    FString ContentPath = PakInternalPath.RightChop(6);
                    PakInternalPath = FString(TEXT("../../../")) + ProjectName + TEXT("/Content/") + ContentPath;
                }

                // Format based on UE5 PAK creation: "SourceFile" "DestinationPath" -compress
                FString ResponseLine = FString::Printf(TEXT("\"%s\" \"%s\" -compress"), *CookedFilePath, *PakInternalPath);
                ResponseLines.Add(ResponseLine);
                UE_LOG(LogNeoPakTools, Log, TEXT("Added to cooked response file: %s"), *ResponseLine);
            }
            else
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("Cooked file does not exist: %s"), *CookedFilePath);
            }
        }
        else
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Empty cooked file path for asset: %s"), *AssetPath);
        }
    }

    if (ResponseLines.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No valid cooked file paths found for response file"));
        return false;
    }

    FString ResponseContent = FString::Join(ResponseLines, TEXT("\n"));

    UE_LOG(LogNeoPakTools, Log, TEXT("Cooked response file content:\n%s"), *ResponseContent);

    if (!FFileHelper::SaveStringToFile(ResponseContent, *ResponseFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to save cooked response file: %s"), *ResponseFilePath);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Created cooked response file: %s with %d entries"), *ResponseFilePath, ResponseLines.Num());
    return true;
}
