// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoCookPakBlueprintLibrary.h"
#include "Utils/NeoCustomCookTool.h"
#include "Utils/NeoCookPakIntegration.h"
#include "Utils/NeoPakFileCreator.h"
#include "NeoPakToolsLog.h"
#include "Interfaces/ITargetPlatformManagerModule.h"
#include "AssetRegistry/AssetRegistryModule.h"

FNeoCookPakResult UNeoCookPakBlueprintLibrary::QuickCookAndPackageDirectory(const FString& DirectoryPath, 
                                                                           const FString& TargetPlatform,
                                                                           const FString& OutputDirectory)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("QuickCookAndPackageDirectory called: %s"), *DirectoryPath);
    
    FNeoCookPakSettings Settings = UNeoCookPakIntegration::GetRecommendedSettings(TEXT("Default"));
    Settings.TargetPlatform = TargetPlatform;
    
    if (!OutputDirectory.IsEmpty())
    {
        Settings.PakOutputDirectory = OutputDirectory;
    }

    return UNeoCookPakIntegration::CookAndPackageDirectory(DirectoryPath, Settings);
}

FNeoCookPakResult UNeoCookPakBlueprintLibrary::QuickCookAndPackageAssets(const TArray<FString>& AssetPaths,
                                                                        const FString& TargetPlatform,
                                                                        const FString& OutputDirectory)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("QuickCookAndPackageAssets called with %d assets"), AssetPaths.Num());
    
    FNeoCookPakSettings Settings = UNeoCookPakIntegration::GetRecommendedSettings(TEXT("Default"));
    Settings.TargetPlatform = TargetPlatform;
    
    if (!OutputDirectory.IsEmpty())
    {
        Settings.PakOutputDirectory = OutputDirectory;
    }

    return UNeoCookPakIntegration::CookAndPackageAssets(AssetPaths, Settings);
}

FNeoCustomCookResult UNeoCookPakBlueprintLibrary::QuickCookDirectory(const FString& DirectoryPath, 
                                                                     const FString& TargetPlatform,
                                                                     const FString& OutputDirectory)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("QuickCookDirectory called: %s"), *DirectoryPath);
    
    FNeoCustomCookSettings Settings;
    Settings.TargetPlatform = TargetPlatform;
    
    if (!OutputDirectory.IsEmpty())
    {
        Settings.OutputDirectory = OutputDirectory;
    }

    return UNeoCustomCookTool::CookDirectory(DirectoryPath, Settings);
}

FNeoCustomCookResult UNeoCookPakBlueprintLibrary::QuickCookAssets(const TArray<FString>& AssetPaths,
                                                                  const FString& TargetPlatform,
                                                                  const FString& OutputDirectory)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("QuickCookAssets called with %d assets"), AssetPaths.Num());
    
    FNeoCustomCookSettings Settings;
    Settings.TargetPlatform = TargetPlatform;
    
    if (!OutputDirectory.IsEmpty())
    {
        Settings.OutputDirectory = OutputDirectory;
    }

    return UNeoCustomCookTool::CookAssets(AssetPaths, Settings);
}

bool UNeoCookPakBlueprintLibrary::QuickCookWithNeoPakCreator(const FString& DirectoryPath, const FString& TargetPlatform)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("QuickCookWithNeoPakCreator called: %s"), *DirectoryPath);
    return UNeoCustomCookTool::CookDirectoryWithNeoPakCreator(DirectoryPath, TargetPlatform);
}

bool UNeoCookPakBlueprintLibrary::QuickCreatePakFile(const TArray<FString>& AssetPaths, const FString& OutputPakFile)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("QuickCreatePakFile called with %d assets"), AssetPaths.Num());
    return FNeoPakFileCreator::CreatePakFileWithCookWorkflow(AssetPaths, OutputPakFile, TEXT("Windows"));
}

TArray<FString> UNeoCookPakBlueprintLibrary::GetAvailableTargetPlatforms()
{
    TArray<FString> PlatformNames;
    
    ITargetPlatformManagerModule* TPM = GetTargetPlatformManager();
    if (TPM)
    {
        const TArray<ITargetPlatform*>& Platforms = TPM->GetTargetPlatforms();
        for (const ITargetPlatform* Platform : Platforms)
        {
            if (Platform)
            {
                PlatformNames.Add(Platform->PlatformName());
            }
        }
    }
    
    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d available target platforms"), PlatformNames.Num());
    return PlatformNames;
}

TArray<FString> UNeoCookPakBlueprintLibrary::GetProjectDirectories()
{
    TArray<FString> Directories;
    
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    TArray<FString> PathList;
    AssetRegistry.GetAllPackagePaths(PathList);

    TSet<FString> UniqueDirectories;
    for (const FString& PackagePath : PathList)
    {
        if (PackagePath.StartsWith(TEXT("/Game/")))
        {
            FString Directory = FPaths::GetPath(PackagePath);
            UniqueDirectories.Add(Directory);
        }
    }

    Directories = UniqueDirectories.Array();
    Directories.Sort();
    
    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d project directories"), Directories.Num());
    return Directories;
}

TArray<FString> UNeoCookPakBlueprintLibrary::GetAssetsInDirectory(const FString& DirectoryPath, bool bRecursive)
{
    return UNeoCustomCookTool::GetAssetsInDirectory(DirectoryPath, bRecursive);
}

FNeoCookPakSettings UNeoCookPakBlueprintLibrary::GetRecommendedSettingsForUseCase(const FString& UseCase)
{
    return UNeoCookPakIntegration::GetRecommendedSettings(UseCase);
}

bool UNeoCookPakBlueprintLibrary::ValidateCookPakSettings(const FNeoCookPakSettings& Settings, FString& OutErrorMessage)
{
    return UNeoCookPakIntegration::ValidateSettings(Settings, OutErrorMessage);
}

FNeoCookPakResult UNeoCookPakBlueprintLibrary::CookAndPackageWithCustomSettings(const FString& DirectoryPath, const FNeoCookPakSettings& Settings)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("CookAndPackageWithCustomSettings called: %s"), *DirectoryPath);
    return UNeoCookPakIntegration::CookAndPackageDirectory(DirectoryPath, Settings);
}

FNeoCustomCookResult UNeoCookPakBlueprintLibrary::CookDirectoryOnly(const FString& DirectoryPath, const FNeoCookPakSettings& Settings)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("CookDirectoryOnly called: %s"), *DirectoryPath);
    return UNeoCookPakIntegration::CookDirectoryOnly(DirectoryPath, Settings);
}

bool UNeoCookPakBlueprintLibrary::CreatePakFromCookedAssets(const FString& CookedDirectory, const FString& OutputPakFile, const FNeoCookPakSettings& Settings)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("CreatePakFromCookedAssets called: %s -> %s"), *CookedDirectory, *OutputPakFile);
    return UNeoCookPakIntegration::CreatePakFromCookedAssets(CookedDirectory, OutputPakFile, Settings);
}
