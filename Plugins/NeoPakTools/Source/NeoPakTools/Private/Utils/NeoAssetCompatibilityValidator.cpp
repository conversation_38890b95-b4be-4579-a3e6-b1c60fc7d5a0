// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoAssetCompatibilityValidator.h"
#include "NeoPakTools.h"
#include "Animation/AnimSequence.h"
#include "Animation/AnimMontage.h"
#include "Engine/SkeletalMesh.h"
#include "Animation/Skeleton.h"

DEFINE_LOG_CATEGORY(LogNeoAssetCompatibilityValidator);

FNeoCompatibilityCheckResult FNeoAssetCompatibilityValidator::IsAnimationCompatibleWithSkeletonConfig(
    UAnimSequence* AnimSequence, 
    UNeoSkeletonPakConfig* SkeletonConfig)
{
    FNeoCompatibilityCheckResult Result;

    if (!AnimSequence)
    {
        Result.ErrorMessages.Add(TEXT("Animation sequence is null"));
        return Result;
    }

    if (!SkeletonConfig)
    {
        Result.ErrorMessages.Add(TEXT("Skeleton config is null"));
        return Result;
    }

    // 获取骨骼配置中的骨骼资产
    USkeleton* ConfigSkeleton = SkeletonConfig->SkeletonAsset.LoadSynchronous();
    if (!ConfigSkeleton)
    {
        Result.ErrorMessages.Add(TEXT("Failed to load skeleton from config"));
        return Result;
    }

    // 检查动画与骨骼的兼容性
    return IsAnimationCompatibleWithSkeleton(AnimSequence, ConfigSkeleton);
}

FNeoCompatibilityCheckResult FNeoAssetCompatibilityValidator::IsAnimMontageCompatibleWithSkeletonConfig(
    UAnimMontage* AnimMontage, 
    UNeoSkeletonPakConfig* SkeletonConfig)
{
    FNeoCompatibilityCheckResult Result;

    if (!AnimMontage)
    {
        Result.ErrorMessages.Add(TEXT("Animation montage is null"));
        return Result;
    }

    if (!SkeletonConfig)
    {
        Result.ErrorMessages.Add(TEXT("Skeleton config is null"));
        return Result;
    }

    // 获取骨骼配置中的骨骼资产
    USkeleton* ConfigSkeleton = SkeletonConfig->SkeletonAsset.LoadSynchronous();
    if (!ConfigSkeleton)
    {
        Result.ErrorMessages.Add(TEXT("Failed to load skeleton from config"));
        return Result;
    }

    // 检查动画蒙太奇的骨骼与配置骨骼的兼容性
    USkeleton* MontageSkeleton = AnimMontage->GetSkeleton();
    if (!MontageSkeleton)
    {
        Result.ErrorMessages.Add(TEXT("Animation montage has no skeleton"));
        return Result;
    }

    return AreSkeletonsCompatible(MontageSkeleton, ConfigSkeleton);
}

FNeoCompatibilityCheckResult FNeoAssetCompatibilityValidator::IsSkeletalMeshCompatibleWithSkeletonConfig(
    USkeletalMesh* SkeletalMesh, 
    UNeoSkeletonPakConfig* SkeletonConfig)
{
    FNeoCompatibilityCheckResult Result;

    if (!SkeletalMesh)
    {
        Result.ErrorMessages.Add(TEXT("Skeletal mesh is null"));
        return Result;
    }

    if (!SkeletonConfig)
    {
        Result.ErrorMessages.Add(TEXT("Skeleton config is null"));
        return Result;
    }

    // 获取骨骼配置中的骨骼资产
    USkeleton* ConfigSkeleton = SkeletonConfig->SkeletonAsset.LoadSynchronous();
    if (!ConfigSkeleton)
    {
        Result.ErrorMessages.Add(TEXT("Failed to load skeleton from config"));
        return Result;
    }

    // 检查骨骼网格与骨骼的兼容性
    return IsSkeletalMeshCompatibleWithSkeleton(SkeletalMesh, ConfigSkeleton);
}

FNeoCompatibilityCheckResult FNeoAssetCompatibilityValidator::AreSkeletonsCompatible(
    USkeleton* Skeleton1, 
    USkeleton* Skeleton2)
{
    FNeoCompatibilityCheckResult Result;

    if (!Skeleton1 || !Skeleton2)
    {
        Result.ErrorMessages.Add(TEXT("One or both skeletons are null"));
        return Result;
    }

    // 如果是同一个骨骼资产，直接兼容
    if (Skeleton1 == Skeleton2)
    {
        Result.bIsCompatible = true;
        Result.DetailedInfo = TEXT("Skeletons are identical");
        return Result;
    }

    // 比较骨骼层次结构
    TArray<FString> MissingBones;
    TArray<FString> ExtraBones;
    bool bHierarchyCompatible = CompareBoneHierarchy(Skeleton1, Skeleton2, MissingBones, ExtraBones);

    if (bHierarchyCompatible)
    {
        Result.bIsCompatible = true;
        Result.DetailedInfo = TEXT("Skeleton hierarchies are compatible");
        
        if (ExtraBones.Num() > 0)
        {
            Result.WarningMessages.Add(FString::Printf(TEXT("Target skeleton has %d extra bones"), ExtraBones.Num()));
        }
    }
    else
    {
        Result.bIsCompatible = false;
        
        if (MissingBones.Num() > 0)
        {
            Result.ErrorMessages.Add(FString::Printf(TEXT("Missing %d bones in target skeleton"), MissingBones.Num()));
            for (int32 i = 0; i < FMath::Min(MissingBones.Num(), 10); ++i)
            {
                Result.ErrorMessages.Add(FString::Printf(TEXT("  Missing bone: %s"), *MissingBones[i]));
            }
            if (MissingBones.Num() > 10)
            {
                Result.ErrorMessages.Add(FString::Printf(TEXT("  ... and %d more"), MissingBones.Num() - 10));
            }
        }
    }

    return Result;
}

FNeoCompatibilityCheckResult FNeoAssetCompatibilityValidator::IsAnimationCompatibleWithSkeleton(
    UAnimSequence* AnimSequence, 
    USkeleton* Skeleton)
{
    FNeoCompatibilityCheckResult Result;

    if (!AnimSequence || !Skeleton)
    {
        Result.ErrorMessages.Add(TEXT("Animation sequence or skeleton is null"));
        return Result;
    }

    // 获取动画的骨骼
    USkeleton* AnimSkeleton = AnimSequence->GetSkeleton();
    if (!AnimSkeleton)
    {
        Result.ErrorMessages.Add(TEXT("Animation sequence has no skeleton"));
        return Result;
    }

    // 检查骨骼兼容性
    FNeoCompatibilityCheckResult SkeletonResult = AreSkeletonsCompatible(AnimSkeleton, Skeleton);
    if (!SkeletonResult.bIsCompatible)
    {
        return SkeletonResult;
    }

    // 检查动画轨道
    TArray<FString> MissingTracks;
    TArray<FString> UnusedTracks;
    bool bTracksCompatible = CheckAnimationTracks(AnimSequence, Skeleton, MissingTracks, UnusedTracks);

    Result.bIsCompatible = bTracksCompatible;
    
    if (MissingTracks.Num() > 0)
    {
        Result.WarningMessages.Add(FString::Printf(TEXT("Animation has %d tracks for bones not in skeleton"), MissingTracks.Num()));
    }
    
    if (UnusedTracks.Num() > 0)
    {
        Result.WarningMessages.Add(FString::Printf(TEXT("Skeleton has %d bones without animation tracks"), UnusedTracks.Num()));
    }

    Result.DetailedInfo = FString::Printf(TEXT("Animation tracks: %d, Skeleton bones: %d"), 
                                         GetAnimationTrackNames(AnimSequence).Num(),
                                         GetBoneNames(Skeleton).Num());

    return Result;
}

FNeoCompatibilityCheckResult FNeoAssetCompatibilityValidator::IsSkeletalMeshCompatibleWithSkeleton(
    USkeletalMesh* SkeletalMesh, 
    USkeleton* Skeleton)
{
    FNeoCompatibilityCheckResult Result;

    if (!SkeletalMesh || !Skeleton)
    {
        Result.ErrorMessages.Add(TEXT("Skeletal mesh or skeleton is null"));
        return Result;
    }

    // 获取骨骼网格的骨骼
    USkeleton* MeshSkeleton = SkeletalMesh->GetSkeleton();
    if (!MeshSkeleton)
    {
        Result.ErrorMessages.Add(TEXT("Skeletal mesh has no skeleton"));
        return Result;
    }

    // 检查骨骼兼容性
    return AreSkeletonsCompatible(MeshSkeleton, Skeleton);
}

bool FNeoAssetCompatibilityValidator::CompareBoneHierarchy(
    USkeleton* Skeleton1, 
    USkeleton* Skeleton2,
    TArray<FString>& OutMissingBones,
    TArray<FString>& OutExtraBones)
{
    if (!Skeleton1 || !Skeleton2)
    {
        return false;
    }

    TArray<FString> Bones1 = GetBoneNames(Skeleton1);
    TArray<FString> Bones2 = GetBoneNames(Skeleton2);

    // 查找缺失的骨骼（在Skeleton1中但不在Skeleton2中）
    for (const FString& Bone : Bones1)
    {
        if (!Bones2.Contains(Bone))
        {
            OutMissingBones.Add(Bone);
        }
    }

    // 查找额外的骨骼（在Skeleton2中但不在Skeleton1中）
    for (const FString& Bone : Bones2)
    {
        if (!Bones1.Contains(Bone))
        {
            OutExtraBones.Add(Bone);
        }
    }

    // 如果没有缺失的骨骼，认为兼容（额外的骨骼是可以接受的）
    return OutMissingBones.Num() == 0;
}

bool FNeoAssetCompatibilityValidator::CheckAnimationTracks(
    UAnimSequence* AnimSequence,
    USkeleton* Skeleton,
    TArray<FString>& OutMissingTracks,
    TArray<FString>& OutUnusedTracks)
{
    if (!AnimSequence || !Skeleton)
    {
        return false;
    }

    TArray<FString> AnimTracks = GetAnimationTrackNames(AnimSequence);
    TArray<FString> SkeletonBones = GetBoneNames(Skeleton);

    // 查找动画中有但骨骼中没有的轨道
    for (const FString& Track : AnimTracks)
    {
        if (!SkeletonBones.Contains(Track))
        {
            OutMissingTracks.Add(Track);
        }
    }

    // 查找骨骼中有但动画中没有的骨骼
    for (const FString& Bone : SkeletonBones)
    {
        if (!AnimTracks.Contains(Bone))
        {
            OutUnusedTracks.Add(Bone);
        }
    }

    return true; // 轨道不匹配不算致命错误
}

TArray<FString> FNeoAssetCompatibilityValidator::GetBoneNames(USkeleton* Skeleton)
{
    TArray<FString> BoneNames;
    
    if (Skeleton)
    {
        const FReferenceSkeleton& RefSkeleton = Skeleton->GetReferenceSkeleton();
        const TArray<FMeshBoneInfo>& BoneInfos = RefSkeleton.GetRefBoneInfo();
        
        for (const FMeshBoneInfo& BoneInfo : BoneInfos)
        {
            BoneNames.Add(BoneInfo.Name.ToString());
        }
    }
    
    return BoneNames;
}

TArray<FString> FNeoAssetCompatibilityValidator::GetAnimationTrackNames(UAnimSequence* AnimSequence)
{
    TArray<FString> TrackNames;

    if (AnimSequence)
    {
        USkeleton* AnimSkeleton = AnimSequence->GetSkeleton();
        if (AnimSkeleton)
        {
            const FReferenceSkeleton& RefSkeleton = AnimSkeleton->GetReferenceSkeleton();
            const TArray<FMeshBoneInfo>& BoneInfos = RefSkeleton.GetRefBoneInfo();

            // 简化实现：直接使用骨骼名称作为轨道名称
            for (const FMeshBoneInfo& BoneInfo : BoneInfos)
            {
                TrackNames.Add(BoneInfo.Name.ToString());
            }
        }
    }

    return TrackNames;
}
