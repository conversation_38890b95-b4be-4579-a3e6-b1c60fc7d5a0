// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoCustomCookTool.h"
#include "Utils/NeoPakFileCreator.h"
#include "NeoPakTools.h"
#include "Engine/Engine.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "UObject/SavePackage.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/PackageName.h"
#include "UObject/LinkerLoad.h"
#include "UObject/UObjectGlobals.h"

UNeoCustomCookTool::UNeoCustomCookTool()
{
}

FNeoCustomCookResult UNeoCustomCookTool::CookDirectory(const FString& DirectoryPath, const FNeoCustomCookSettings& Settings)
{
    FNeoCustomCookResult Result;
    
    UE_LOG(LogNeoPakTools, Log, TEXT("Starting custom cook for directory: %s"), *DirectoryPath);
    
    // Validate settings
    FString ValidationError;
    if (!ValidateCookSettings(Settings, ValidationError))
    {
        Result.ErrorMessage = ValidationError;
        UE_LOG(LogNeoPakTools, Error, TEXT("Cook settings validation failed: %s"), *ValidationError);
        return Result;
    }

    // Get all assets in directory
    TArray<FString> AssetPaths = GetAssetsInDirectory(DirectoryPath, true);
    if (AssetPaths.Num() == 0)
    {
        Result.ErrorMessage = FString::Printf(TEXT("No assets found in directory: %s"), *DirectoryPath);
        UE_LOG(LogNeoPakTools, Warning, TEXT("%s"), *Result.ErrorMessage);
        return Result;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d assets in directory: %s"), AssetPaths.Num(), *DirectoryPath);

    // Cook all assets
    return CookAssets(AssetPaths, Settings);
}

FNeoCustomCookResult UNeoCustomCookTool::CookAssets(const TArray<FString>& AssetPaths, const FNeoCustomCookSettings& Settings)
{
    FNeoCustomCookResult Result;
    
    UE_LOG(LogNeoPakTools, Log, TEXT("Starting custom cook for %d assets"), AssetPaths.Num());
    
    // Initialize Cook environment
    const ITargetPlatform* TargetPlatform = nullptr;
    if (!InitializeCookEnvironment(Settings, TargetPlatform))
    {
        Result.ErrorMessage = TEXT("Failed to initialize cook environment");
        UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        return Result;
    }

    // Create output directory
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.CreateDirectoryTree(*Settings.OutputDirectory))
    {
        Result.ErrorMessage = FString::Printf(TEXT("Failed to create output directory: %s"), *Settings.OutputDirectory);
        UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        return Result;
    }

    // Cook each asset
    TSet<UPackage*> ProcessedPackages;
    
    for (const FString& AssetPath : AssetPaths)
    {
        if (ShouldExcludePackage(AssetPath, Settings))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Excluding package: %s"), *AssetPath);
            continue;
        }

        // Load package
        UPackage* Package = LoadPackage(nullptr, *AssetPath, LOAD_None);
        if (!Package)
        {
            Result.FailedPackages.Add(AssetPath);
            UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to load package: %s"), *AssetPath);
            continue;
        }

        // Avoid duplicate processing
        if (ProcessedPackages.Contains(Package))
        {
            continue;
        }
        ProcessedPackages.Add(Package);

        // Cook package
        FString CookError;
        if (CookSinglePackage(Package, TargetPlatform, Settings, CookError))
        {
            Result.CookedPackages.Add(AssetPath);
            Result.CookedPackageCount++;
            UE_LOG(LogNeoPakTools, Log, TEXT("Successfully cooked package: %s"), *AssetPath);
            
            // Process dependencies
            if (Settings.bIncludeDependencies)
            {
                TArray<UPackage*> Dependencies;
                CollectDependencies(Package, Dependencies);
                
                for (UPackage* DepPackage : Dependencies)
                {
                    if (!ProcessedPackages.Contains(DepPackage))
                    {
                        ProcessedPackages.Add(DepPackage);
                        FString DepError;
                        if (CookSinglePackage(DepPackage, TargetPlatform, Settings, DepError))
                        {
                            Result.CookedPackages.Add(DepPackage->GetName());
                            Result.CookedPackageCount++;
                            UE_LOG(LogNeoPakTools, Log, TEXT("Successfully cooked dependency: %s"), *DepPackage->GetName());
                        }
                        else
                        {
                            UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to cook dependency %s: %s"), *DepPackage->GetName(), *DepError);
                        }
                    }
                }
            }
        }
        else
        {
            Result.FailedPackages.Add(AssetPath);
            UE_LOG(LogNeoPakTools, Error, TEXT("Failed to cook package %s: %s"), *AssetPath, *CookError);
        }
    }

    Result.bSuccess = Result.FailedPackages.Num() == 0;
    
    UE_LOG(LogNeoPakTools, Log, TEXT("Custom cook completed. Success: %d, Failed: %d"), 
           Result.CookedPackageCount, Result.FailedPackages.Num());

    return Result;
}

FNeoCustomCookResult UNeoCustomCookTool::CookPackage(UPackage* Package, const FNeoCustomCookSettings& Settings)
{
    FNeoCustomCookResult Result;
    
    if (!Package)
    {
        Result.ErrorMessage = TEXT("Invalid package");
        UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        return Result;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Starting custom cook for package: %s"), *Package->GetName());

    const ITargetPlatform* TargetPlatform = nullptr;
    if (!InitializeCookEnvironment(Settings, TargetPlatform))
    {
        Result.ErrorMessage = TEXT("Failed to initialize cook environment");
        UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        return Result;
    }

    FString CookError;
    if (CookSinglePackage(Package, TargetPlatform, Settings, CookError))
    {
        Result.bSuccess = true;
        Result.CookedPackageCount = 1;
        Result.CookedPackages.Add(Package->GetName());
        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully cooked package: %s"), *Package->GetName());
    }
    else
    {
        Result.ErrorMessage = CookError;
        Result.FailedPackages.Add(Package->GetName());
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to cook package %s: %s"), *Package->GetName(), *CookError);
    }

    return Result;
}

bool UNeoCustomCookTool::CookDirectoryWithNeoPakCreator(const FString& DirectoryPath, const FString& TargetPlatform)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Cooking directory with NeoPakFileCreator: %s"), *DirectoryPath);
    return FNeoPakFileCreator::CookDirectory(DirectoryPath, TargetPlatform);
}

bool UNeoCustomCookTool::CookAssetsWithNeoPakCreator(const TArray<FString>& AssetPaths, const FString& TargetPlatform)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Cooking %d assets with NeoPakFileCreator"), AssetPaths.Num());
    return FNeoPakFileCreator::CookAssets(AssetPaths, TargetPlatform);
}

bool UNeoCustomCookTool::InitializeCookEnvironment(const FNeoCustomCookSettings& Settings, const ITargetPlatform*& OutTargetPlatform)
{
    // Get target platform manager
    ITargetPlatformManagerModule* TPM = GetTargetPlatformManager();
    if (!TPM)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to get TargetPlatformManager"));
        return false;
    }

    // Find target platform
    OutTargetPlatform = TPM->FindTargetPlatform(*Settings.TargetPlatform);
    if (!OutTargetPlatform)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Target platform not found: %s"), *Settings.TargetPlatform);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Initialized cook environment for platform: %s"), *Settings.TargetPlatform);
    return true;
}

bool UNeoCustomCookTool::CookSinglePackage(UPackage* Package, const ITargetPlatform* TargetPlatform,
                                          const FNeoCustomCookSettings& Settings, FString& OutError)
{
    if (!Package || !TargetPlatform)
    {
        OutError = TEXT("Invalid package or target platform");
        return false;
    }

    // Check if it's a transient package
    if (Package == GetTransientPackage())
    {
        OutError = TEXT("Cannot cook transient package");
        return false;
    }

    try
    {
        // Get output path
        FString OutputPath = GetCookedPackagePath(Package->GetName(), Settings);

        // Save cooked package (simplified approach without advanced Cook context)
        return SaveCookedPackage(Package, TargetPlatform, OutputPath, Settings);
    }
    catch (...)
    {
        OutError = TEXT("Exception during cooking");
        return false;
    }
}

bool UNeoCustomCookTool::SaveCookedPackage(UPackage* Package, const ITargetPlatform* TargetPlatform,
                                           const FString& OutputPath, const FNeoCustomCookSettings& Settings)
{
    // Configure save parameters
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.SaveFlags = SAVE_NoError | SAVE_Async;

    if (Settings.bUnversioned)
    {
        SaveArgs.SaveFlags |= SAVE_Unversioned;
    }

    if (Settings.bCompressed)
    {
        // Note: Package compression is handled differently in UE5
        // SaveArgs.SaveFlags |= SAVE_CompressPackage; // This flag may not be available in UE5
    }

    // Save package
    UPackage::SavePackage(Package, nullptr, *OutputPath, SaveArgs);

    UE_LOG(LogNeoPakTools, Log, TEXT("Cooked package saved: %s -> %s"), *Package->GetName(), *OutputPath);
    return true;
}

void UNeoCustomCookTool::CollectDependencies(UPackage* Package, TArray<UPackage*>& OutDependencies)
{
    if (!Package)
    {
        return;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Collecting dependencies for package: %s"), *Package->GetName());

    // Get all objects in package
    TArray<UObject*> Objects;
    GetObjectsWithOuter(Package, Objects, true);

    TSet<UPackage*> FoundDependencies;

    for (UObject* Object : Objects)
    {
        if (!Object)
        {
            continue;
        }

        // Collect objects referenced by this object
        TArray<UObject*> ReferencedObjects;
        FReferenceFinder ObjectReferenceCollector(ReferencedObjects, Object, false, true, true, true);
        ObjectReferenceCollector.FindReferences(Object);

        for (UObject* ReferencedObject : ReferencedObjects)
        {
            if (ReferencedObject && ReferencedObject->GetPackage() != Package)
            {
                UPackage* ReferencedPackage = ReferencedObject->GetPackage();
                if (ReferencedPackage && ReferencedPackage != GetTransientPackage())
                {
                    FoundDependencies.Add(ReferencedPackage);
                }
            }
        }
    }

    OutDependencies = FoundDependencies.Array();
    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d dependencies for package: %s"), OutDependencies.Num(), *Package->GetName());
}

FString UNeoCustomCookTool::GetCookedPackagePath(const FString& PackageName, const FNeoCustomCookSettings& Settings)
{
    FString CleanPackageName = PackageName;
    if (CleanPackageName.StartsWith(TEXT("/Game/")))
    {
        CleanPackageName = CleanPackageName.RightChop(6); // Remove "/Game/"
    }

    FString OutputPath = FPaths::Combine(Settings.OutputDirectory, Settings.TargetPlatform, CleanPackageName + TEXT(".uasset"));
    return OutputPath;
}

bool UNeoCustomCookTool::ShouldExcludePackage(const FString& PackagePath, const FNeoCustomCookSettings& Settings)
{
    for (const FString& Pattern : Settings.ExcludePatterns)
    {
        if (PackagePath.Contains(Pattern))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Excluding package %s due to pattern: %s"), *PackagePath, *Pattern);
            return true;
        }
    }
    return false;
}

TArray<FString> UNeoCustomCookTool::GetAssetsInDirectory(const FString& DirectoryPath, bool bRecursive)
{
    TArray<FString> AssetPaths;

    UE_LOG(LogNeoPakTools, Log, TEXT("Getting assets in directory: %s (recursive: %s)"), *DirectoryPath, bRecursive ? TEXT("true") : TEXT("false"));

    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Ensure asset registry has scanned
    AssetRegistry.SearchAllAssets(true);

    // Get assets in directory
    TArray<FAssetData> AssetDataList;
    FString SearchPath = DirectoryPath;
    if (!SearchPath.StartsWith(TEXT("/Game/")))
    {
        SearchPath = TEXT("/Game/") + SearchPath;
    }

    AssetRegistry.GetAssetsByPath(*SearchPath, AssetDataList, bRecursive);

    for (const FAssetData& AssetData : AssetDataList)
    {
        AssetPaths.Add(AssetData.PackageName.ToString());
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d assets in directory: %s"), AssetPaths.Num(), *DirectoryPath);
    return AssetPaths;
}

bool UNeoCustomCookTool::ValidateCookSettings(const FNeoCustomCookSettings& Settings, FString& OutErrorMessage)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Validating cook settings"));

    if (Settings.OutputDirectory.IsEmpty())
    {
        OutErrorMessage = TEXT("Output directory cannot be empty");
        return false;
    }

    if (Settings.TargetPlatform.IsEmpty())
    {
        OutErrorMessage = TEXT("Target platform cannot be empty");
        return false;
    }

    // Check if target platform is valid
    ITargetPlatformManagerModule* TPM = GetTargetPlatformManager();
    if (!TPM)
    {
        OutErrorMessage = TEXT("TargetPlatformManager not available");
        return false;
    }

    const ITargetPlatform* TargetPlatform = TPM->FindTargetPlatform(*Settings.TargetPlatform);
    if (!TargetPlatform)
    {
        OutErrorMessage = FString::Printf(TEXT("Invalid target platform: %s"), *Settings.TargetPlatform);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Cook settings validation passed"));
    return true;
}
