// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoCookPakIntegration.h"
#include "Utils/NeoCustomCookTool.h"
#include "Utils/NeoPakFileCreator.h"
#include "NeoPakTools.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"

UNeoCookPakIntegration::UNeoCookPakIntegration()
{
}

FNeoCookPakResult UNeoCookPakIntegration::CookAndPackageDirectory(const FString& DirectoryPath, const FNeoCookPakSettings& Settings)
{
    FNeoCookPakResult Result;
    
    UE_LOG(LogNeoPakTools, Log, TEXT("Starting Cook+PAK workflow for directory: %s"), *DirectoryPath);
    
    // Validate settings
    FString ValidationError;
    if (!ValidateSettings(Settings, ValidationError))
    {
        Result.ErrorMessage = ValidationError;
        UE_LOG(LogNeoPakTools, Error, TEXT("Settings validation failed: %s"), *ValidationError);
        return Result;
    }

    if (Settings.bUseNeoPakCreatorWorkflow)
    {
        // Use NeoPakFileCreator's integrated Cook+PAK workflow
        UE_LOG(LogNeoPakTools, Log, TEXT("Using NeoPakFileCreator Cook+PAK workflow"));
        
        // Get assets in directory
        TArray<FString> AssetPaths = UNeoCustomCookTool::GetAssetsInDirectory(DirectoryPath, true);
        if (AssetPaths.Num() == 0)
        {
            Result.ErrorMessage = FString::Printf(TEXT("No assets found in directory: %s"), *DirectoryPath);
            UE_LOG(LogNeoPakTools, Warning, TEXT("%s"), *Result.ErrorMessage);
            return Result;
        }

        // Generate PAK file name
        FString PakFileName = GeneratePakFileName(DirectoryPath, Settings);
        FString OutputPakFile = FPaths::Combine(Settings.PakOutputDirectory, PakFileName);

        // First cook the assets, then create PAK
        bool bCookSuccess = FNeoPakFileCreator::CookAssets(AssetPaths, Settings.TargetPlatform);
        bool bPakSuccess = false;

        if (bCookSuccess)
        {
            bPakSuccess = FNeoPakFileCreator::CreatePakFromAssets(AssetPaths, OutputPakFile);
        }
        
        if (bCookSuccess && bPakSuccess)
        {
            Result.bSuccess = true;
            Result.bCookSuccess = bCookSuccess;
            Result.bPakSuccess = bPakSuccess;
            Result.CookedAssetCount = AssetPaths.Num();
            Result.CookedAssets = AssetPaths;
            Result.GeneratedPakFile = OutputPakFile;
            UE_LOG(LogNeoPakTools, Log, TEXT("Cook+PAK workflow completed successfully. PAK file: %s"), *OutputPakFile);
        }
        else
        {
            Result.ErrorMessage = TEXT("NeoPakFileCreator Cook+PAK workflow failed");
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        }
    }
    else
    {
        // Use custom cooking workflow
        UE_LOG(LogNeoPakTools, Log, TEXT("Using custom cooking workflow"));
        
        // Step 1: Cook assets
        FNeoCustomCookSettings CookSettings = ConvertToCustomCookSettings(Settings);
        FNeoCustomCookResult CookResult = UNeoCustomCookTool::CookDirectory(DirectoryPath, CookSettings);
        
        Result.CookResult = CookResult;
        Result.bCookSuccess = CookResult.bSuccess;
        Result.CookedAssetCount = CookResult.CookedPackageCount;
        Result.CookedAssets = CookResult.CookedPackages;
        Result.FailedAssets = CookResult.FailedPackages;

        if (!CookResult.bSuccess)
        {
            Result.ErrorMessage = FString::Printf(TEXT("Cooking failed: %s"), *CookResult.ErrorMessage);
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
            return Result;
        }

        // Step 2: Create PAK from cooked assets
        FString PakFileName = GeneratePakFileName(DirectoryPath, Settings);
        FString OutputPakFile = FPaths::Combine(Settings.PakOutputDirectory, PakFileName);
        
        bool bPakSuccess = CreatePakFromCookedAssets(CookSettings.OutputDirectory, OutputPakFile, Settings);
        Result.bPakSuccess = bPakSuccess;
        
        if (bPakSuccess)
        {
            Result.bSuccess = true;
            Result.GeneratedPakFile = OutputPakFile;
            UE_LOG(LogNeoPakTools, Log, TEXT("Custom Cook+PAK workflow completed successfully. PAK file: %s"), *OutputPakFile);
        }
        else
        {
            Result.ErrorMessage = TEXT("PAK creation failed after successful cooking");
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        }
    }

    return Result;
}

FNeoCookPakResult UNeoCookPakIntegration::CookAndPackageAssets(const TArray<FString>& AssetPaths, const FNeoCookPakSettings& Settings)
{
    FNeoCookPakResult Result;
    
    UE_LOG(LogNeoPakTools, Log, TEXT("Starting Cook+PAK workflow for %d assets"), AssetPaths.Num());
    
    // Validate settings
    FString ValidationError;
    if (!ValidateSettings(Settings, ValidationError))
    {
        Result.ErrorMessage = ValidationError;
        UE_LOG(LogNeoPakTools, Error, TEXT("Settings validation failed: %s"), *ValidationError);
        return Result;
    }

    if (AssetPaths.Num() == 0)
    {
        Result.ErrorMessage = TEXT("No assets provided for cooking");
        UE_LOG(LogNeoPakTools, Warning, TEXT("%s"), *Result.ErrorMessage);
        return Result;
    }

    if (Settings.bUseNeoPakCreatorWorkflow)
    {
        // Use NeoPakFileCreator's integrated Cook+PAK workflow
        UE_LOG(LogNeoPakTools, Log, TEXT("Using NeoPakFileCreator Cook+PAK workflow for assets"));
        
        // Generate PAK file name from first asset path
        FString PakFileName = FPaths::GetBaseFilename(AssetPaths[0]) + TEXT("_Assets.pak");
        FString OutputPakFile = FPaths::Combine(Settings.PakOutputDirectory, PakFileName);

        // First cook the assets, then create PAK
        bool bCookSuccess = FNeoPakFileCreator::CookAssets(AssetPaths, Settings.TargetPlatform);
        bool bPakSuccess = false;

        if (bCookSuccess)
        {
            bPakSuccess = FNeoPakFileCreator::CreatePakFromAssets(AssetPaths, OutputPakFile);
        }
        
        if (bPakSuccess)
        {
            Result.bSuccess = true;
            Result.bCookSuccess = true;
            Result.bPakSuccess = true;
            Result.CookedAssetCount = AssetPaths.Num();
            Result.CookedAssets = AssetPaths;
            Result.GeneratedPakFile = OutputPakFile;
            UE_LOG(LogNeoPakTools, Log, TEXT("Cook+PAK workflow completed successfully. PAK file: %s"), *OutputPakFile);
        }
        else
        {
            Result.ErrorMessage = TEXT("NeoPakFileCreator Cook+PAK workflow failed");
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        }
    }
    else
    {
        // Use custom cooking workflow
        UE_LOG(LogNeoPakTools, Log, TEXT("Using custom cooking workflow for assets"));
        
        // Step 1: Cook assets
        FNeoCustomCookSettings CookSettings = ConvertToCustomCookSettings(Settings);
        FNeoCustomCookResult CookResult = UNeoCustomCookTool::CookAssets(AssetPaths, CookSettings);
        
        Result.CookResult = CookResult;
        Result.bCookSuccess = CookResult.bSuccess;
        Result.CookedAssetCount = CookResult.CookedPackageCount;
        Result.CookedAssets = CookResult.CookedPackages;
        Result.FailedAssets = CookResult.FailedPackages;

        if (!CookResult.bSuccess)
        {
            Result.ErrorMessage = FString::Printf(TEXT("Cooking failed: %s"), *CookResult.ErrorMessage);
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
            return Result;
        }

        // Step 2: Create PAK from cooked assets
        FString PakFileName = FPaths::GetBaseFilename(AssetPaths[0]) + TEXT("_Assets.pak");
        FString OutputPakFile = FPaths::Combine(Settings.PakOutputDirectory, PakFileName);
        
        bool bPakSuccess = CreatePakFromCookedAssets(CookSettings.OutputDirectory, OutputPakFile, Settings);
        Result.bPakSuccess = bPakSuccess;
        
        if (bPakSuccess)
        {
            Result.bSuccess = true;
            Result.GeneratedPakFile = OutputPakFile;
            UE_LOG(LogNeoPakTools, Log, TEXT("Custom Cook+PAK workflow completed successfully. PAK file: %s"), *OutputPakFile);
        }
        else
        {
            Result.ErrorMessage = TEXT("PAK creation failed after successful cooking");
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *Result.ErrorMessage);
        }
    }

    return Result;
}

FNeoCustomCookResult UNeoCookPakIntegration::CookDirectoryOnly(const FString& DirectoryPath, const FNeoCookPakSettings& Settings)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Cooking directory only (no PAK): %s"), *DirectoryPath);

    FNeoCustomCookSettings CookSettings = ConvertToCustomCookSettings(Settings);
    return UNeoCustomCookTool::CookDirectory(DirectoryPath, CookSettings);
}

bool UNeoCookPakIntegration::CreatePakFromCookedAssets(const FString& CookedDirectory, const FString& OutputPakFile, const FNeoCookPakSettings& Settings)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating PAK from cooked assets: %s -> %s"), *CookedDirectory, *OutputPakFile);

    // Find all cooked files in the directory
    TArray<FString> CookedFiles;
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    // Search for .uasset, .umap, .uexp files
    TArray<FString> Extensions = {TEXT("*.uasset"), TEXT("*.umap"), TEXT("*.uexp"), TEXT("*.ubulk")};

    for (const FString& Extension : Extensions)
    {
        TArray<FString> FoundFiles;
        PlatformFile.FindFilesRecursively(FoundFiles, *CookedDirectory, *Extension);
        CookedFiles.Append(FoundFiles);
    }

    if (CookedFiles.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("No cooked files found in directory: %s"), *CookedDirectory);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d cooked files to package"), CookedFiles.Num());

    // Use NeoPakFileCreator to create PAK from cooked files
    // Note: This is a simplified approach - we'll need to implement CreatePakFromFiles later
    // For now, we'll use the existing CreatePakFromAssets method
    TArray<FString> AssetPaths;
    // Convert file paths back to asset paths (simplified)
    for (const FString& FilePath : CookedFiles)
    {
        // This is a placeholder - proper implementation would convert cooked file paths back to asset paths
        AssetPaths.Add(FilePath);
    }
    return FNeoPakFileCreator::CreatePakFromAssets(AssetPaths, OutputPakFile);
}

bool UNeoCookPakIntegration::ValidateSettings(const FNeoCookPakSettings& Settings, FString& OutErrorMessage)
{
    if (Settings.TargetPlatform.IsEmpty())
    {
        OutErrorMessage = TEXT("Target platform cannot be empty");
        return false;
    }

    if (Settings.CookOutputDirectory.IsEmpty())
    {
        OutErrorMessage = TEXT("Cook output directory cannot be empty");
        return false;
    }

    if (Settings.PakOutputDirectory.IsEmpty())
    {
        OutErrorMessage = TEXT("PAK output directory cannot be empty");
        return false;
    }

    // Validate custom cook settings if not using NeoPakCreator workflow
    if (!Settings.bUseNeoPakCreatorWorkflow)
    {
        FNeoCustomCookSettings CookSettings = ConvertToCustomCookSettings(Settings);
        return UNeoCustomCookTool::ValidateCookSettings(CookSettings, OutErrorMessage);
    }

    return true;
}

FNeoCookPakSettings UNeoCookPakIntegration::GetRecommendedSettings(const FString& UseCase)
{
    FNeoCookPakSettings Settings;

    if (UseCase == TEXT("Development"))
    {
        Settings.bUnversioned = true;
        Settings.bCompressed = false;
        Settings.bIncludeDependencies = true;
        Settings.bUseNeoPakCreatorWorkflow = true;
        Settings.CompressionLevel = 1; // Fast compression for development
    }
    else if (UseCase == TEXT("Shipping"))
    {
        Settings.bUnversioned = true;
        Settings.bCompressed = true;
        Settings.bIncludeDependencies = true;
        Settings.bUseNeoPakCreatorWorkflow = true;
        Settings.CompressionLevel = 6; // High compression for shipping
    }
    else if (UseCase == TEXT("Testing"))
    {
        Settings.bUnversioned = false;
        Settings.bCompressed = false;
        Settings.bIncludeDependencies = false;
        Settings.bUseNeoPakCreatorWorkflow = false; // Use custom workflow for testing
        Settings.CompressionLevel = 0; // No compression for testing
    }
    else // Default
    {
        Settings.bUnversioned = true;
        Settings.bCompressed = false;
        Settings.bIncludeDependencies = true;
        Settings.bUseNeoPakCreatorWorkflow = true;
        Settings.CompressionLevel = 4; // Balanced compression
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Generated recommended settings for use case: %s"), *UseCase);
    return Settings;
}

FNeoCustomCookSettings UNeoCookPakIntegration::ConvertToCustomCookSettings(const FNeoCookPakSettings& Settings)
{
    FNeoCustomCookSettings CookSettings;
    CookSettings.OutputDirectory = Settings.CookOutputDirectory;
    CookSettings.TargetPlatform = Settings.TargetPlatform;
    CookSettings.bUnversioned = Settings.bUnversioned;
    CookSettings.bCompressed = Settings.bCompressed;
    CookSettings.bIncludeDependencies = Settings.bIncludeDependencies;
    CookSettings.ExcludePatterns = Settings.ExcludePatterns;
    return CookSettings;
}

FString UNeoCookPakIntegration::GeneratePakFileName(const FString& DirectoryPath, const FNeoCookPakSettings& Settings)
{
    FString CleanDirectoryName = FPaths::GetCleanFilename(DirectoryPath);
    if (CleanDirectoryName.IsEmpty())
    {
        CleanDirectoryName = TEXT("Assets");
    }

    FString PakFileName = FString::Printf(TEXT("%s_%s.pak"), *CleanDirectoryName, *Settings.TargetPlatform);
    return PakFileName;
}

void UNeoCookPakIntegration::CleanupTempFiles(const TArray<FString>& FilesToClean)
{
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    for (const FString& FilePath : FilesToClean)
    {
        if (PlatformFile.FileExists(*FilePath))
        {
            if (PlatformFile.DeleteFile(*FilePath))
            {
                UE_LOG(LogNeoPakTools, Log, TEXT("Cleaned up temp file: %s"), *FilePath);
            }
            else
            {
                UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to clean up temp file: %s"), *FilePath);
            }
        }
    }
}
