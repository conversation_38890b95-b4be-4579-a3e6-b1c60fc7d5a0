// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoAssetDependencyResolver.h"
#include "NeoPakTools.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetRegistry/IAssetRegistry.h"
#include "Engine/AssetManager.h"
#include "Misc/PackageName.h"
#include "Animation/Skeleton.h"
#include "Config/NeoSkeletonPakConfig.h"
#include "Config/NeoCharacterPakConfig.h"
#include "Config/NeoAnimSequencePakConfig.h"
#include "Config/NeoAnimMontagePakConfig.h"
#include "Config/NeoClothingPakConfig.h"

TArray<FString> FNeoAssetDependencyResolver::ResolveDependencies(const FString& AssetPath)
{
    TArray<FString> Dependencies;

    UE_LOG(LogNeoPakTools, Log, TEXT("Resolving dependencies for asset: %s"), *AssetPath);

    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // 转换资产路径为包名
    FString PackageName = AssetPath;

    // 如果路径包含对象名（如 /Game/Maps/TestMap.TestMap），提取包名
    int32 DotIndex = PackageName.Find(TEXT("."), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
    if (DotIndex != INDEX_NONE)
    {
        PackageName = PackageName.Left(DotIndex);
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("  Package name: %s"), *PackageName);

    // 获取资产的依赖关系
    TArray<FName> PackageDependencies;
    FName PackageNameFName(*PackageName);

    // 获取包依赖
    if (AssetRegistry.GetDependencies(PackageNameFName, PackageDependencies))
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("  Found %d package dependencies"), PackageDependencies.Num());

        for (const FName& Dependency : PackageDependencies)
        {
            FString DependencyString = Dependency.ToString();
            Dependencies.Add(DependencyString);
            UE_LOG(LogNeoPakTools, Log, TEXT("    Dependency: %s"), *DependencyString);
        }
    }
    else
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("  No dependencies found or failed to get dependencies"));
    }

    return Dependencies;
}

bool FNeoAssetDependencyResolver::ValidateDependencies(const TArray<FString>& AssetPaths)
{
    for (const FString& AssetPath : AssetPaths)
    {
        if (!FPackageName::DoesPackageExist(AssetPath))
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Missing dependency: %s"), *AssetPath);
            return false;
        }
    }
    return true;
}

TArray<UNeoPakConfigAssetBase*> FNeoAssetDependencyResolver::GetPackagingOrder(const TArray<UNeoPakConfigAssetBase*>& ConfigAssets)
{
    TArray<UNeoPakConfigAssetBase*> OrderedConfigs;
    TArray<UNeoPakConfigAssetBase*> RemainingConfigs = ConfigAssets;
    
    // 简单的依赖排序：骨骼资产优先，然后是其他资产
    while (RemainingConfigs.Num() > 0)
    {
        bool bFoundIndependent = false;
        
        for (int32 i = RemainingConfigs.Num() - 1; i >= 0; i--)
        {
            UNeoPakConfigAssetBase* Config = RemainingConfigs[i];
            
            // 检查是否为骨骼配置（无依赖）
            if (Cast<UNeoSkeletonPakConfig>(Config))
            {
                OrderedConfigs.Add(Config);
                RemainingConfigs.RemoveAt(i);
                bFoundIndependent = true;
                continue;
            }
            
            // 检查其他配置的依赖是否已经在OrderedConfigs中
            bool bDependenciesSatisfied = true;
            for (UNeoPakConfigAssetBase* OrderedConfig : OrderedConfigs)
            {
                if (HasDependency(Config, OrderedConfig))
                {
                    // 依赖已满足
                    continue;
                }
            }
            
            if (bDependenciesSatisfied)
            {
                OrderedConfigs.Add(Config);
                RemainingConfigs.RemoveAt(i);
                bFoundIndependent = true;
            }
        }
        
        // 如果没有找到可以处理的配置，可能存在循环依赖
        if (!bFoundIndependent)
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("Possible circular dependency detected, adding remaining configs"));
            OrderedConfigs.Append(RemainingConfigs);
            break;
        }
    }
    
    return OrderedConfigs;
}

FNeoDependencyCheckResult FNeoAssetDependencyResolver::CheckDataAssetDirectoryDependencies(UNeoPakConfigAssetBase* ConfigAsset, bool bAutoCheck)
{
    FNeoDependencyCheckResult Result;

    if (!ConfigAsset)
    {
        Result.ErrorMessages.Add(TEXT("ConfigAsset is null"));
        return Result;
    }

    // 获取DataAsset所在目录
    FString DataAssetDirectory = GetDataAssetDirectory(ConfigAsset);
    if (DataAssetDirectory.IsEmpty())
    {
        Result.ErrorMessages.Add(TEXT("Failed to get DataAsset directory"));
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to get DataAsset directory for config: %s"), *ConfigAsset->ConfigName);
        return Result;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Checking dependencies for DataAsset '%s' in directory: %s"),
           *ConfigAsset->ConfigName, *DataAssetDirectory);

    // 获取要打包的资产列表
    TArray<FSoftObjectPath> AssetsToPackage = ConfigAsset->GetAssetsToPackage();
    if (AssetsToPackage.Num() == 0)
    {
        Result.ErrorMessages.Add(TEXT("No assets to package found"));
        UE_LOG(LogNeoPakTools, Warning, TEXT("No assets to package found for config: %s"), *ConfigAsset->ConfigName);
        Result.bCheckPassed = true; // 没有资产也算通过
        return Result;
    }

    int32 TotalDependenciesChecked = 0;
    int32 ExcludedDependencies = 0;

    for (const FSoftObjectPath& AssetPath : AssetsToPackage)
    {
        UE_LOG(LogNeoPakTools, Verbose, TEXT("Checking dependencies for asset: %s"), *AssetPath.ToString());

        // 解析资产的所有依赖
        TArray<FString> Dependencies = ResolveDependencies(AssetPath.ToString());
        TotalDependenciesChecked += Dependencies.Num();

        for (const FString& DependencyPath : Dependencies)
        {
            // 跳过排除的资产类型
            if (IsExcludedAsset(DependencyPath))
            {
                ExcludedDependencies++;
                UE_LOG(LogNeoPakTools, VeryVerbose, TEXT("Skipping excluded asset: %s"), *DependencyPath);
                continue;
            }

            // 检查依赖是否在DataAsset目录下
            if (!IsAssetInDirectory(DependencyPath, DataAssetDirectory))
            {
                Result.OutOfDirectoryDependencies.AddUnique(DependencyPath);
                Result.ErrorMessages.Add(FString::Printf(TEXT("Dependency '%s' is not in DataAsset directory '%s'"),
                                                        *DependencyPath, *DataAssetDirectory));
                UE_LOG(LogNeoPakTools, Warning, TEXT("Out-of-directory dependency: %s"), *DependencyPath);
            }

            // 检查依赖是否存在
            if (!FPackageName::DoesPackageExist(DependencyPath))
            {
                Result.MissingDependencies.AddUnique(DependencyPath);
                Result.ErrorMessages.Add(FString::Printf(TEXT("Missing dependency: %s"), *DependencyPath));
                UE_LOG(LogNeoPakTools, Error, TEXT("Missing dependency: %s"), *DependencyPath);
            }
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Dependency check summary - Total: %d, Excluded: %d, Missing: %d, OutOfDirectory: %d"),
           TotalDependenciesChecked, ExcludedDependencies,
           Result.MissingDependencies.Num(), Result.OutOfDirectoryDependencies.Num());

    // 设置检查结果
    Result.bCheckPassed = (Result.OutOfDirectoryDependencies.Num() == 0 && Result.MissingDependencies.Num() == 0);

    // 如果是自动检查且检查失败，记录错误
    if (bAutoCheck && !Result.bCheckPassed)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Directory dependency check failed for DataAsset: %s"), *ConfigAsset->ConfigName);
        for (const FString& ErrorMsg : Result.ErrorMessages)
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("  %s"), *ErrorMsg);
        }
    }

    return Result;
}

FNeoDependencyCheckResult FNeoAssetDependencyResolver::ManualCheckDataAssetDependencies(UNeoPakConfigAssetBase* ConfigAsset)
{
    return CheckDataAssetDirectoryDependencies(ConfigAsset, false);
}

bool FNeoAssetDependencyResolver::HasDependency(UNeoPakConfigAssetBase* ConfigA, UNeoPakConfigAssetBase* ConfigB)
{
    // 检查ConfigA是否依赖ConfigB
    if (UNeoCharacterPakConfig* CharacterConfig = Cast<UNeoCharacterPakConfig>(ConfigA))
    {
        return CharacterConfig->RequiredSkeletonConfig.Get() == ConfigB;
    }
    else if (UNeoAnimSequencePakConfig* AnimSeqConfig = Cast<UNeoAnimSequencePakConfig>(ConfigA))
    {
        return AnimSeqConfig->RequiredSkeletonConfig.Get() == ConfigB;
    }
    else if (UNeoAnimMontagePakConfig* AnimMontageConfig = Cast<UNeoAnimMontagePakConfig>(ConfigA))
    {
        return AnimMontageConfig->RequiredSkeletonConfig.Get() == ConfigB;
    }
    else if (UNeoClothingPakConfig* ClothingConfig = Cast<UNeoClothingPakConfig>(ConfigA))
    {
        return ClothingConfig->RequiredSkeletonConfig.Get() == ConfigB;
    }
    
    return false;
}

FString FNeoAssetDependencyResolver::GetDataAssetDirectory(UNeoPakConfigAssetBase* ConfigAsset)
{
    if (!ConfigAsset)
    {
        return FString();
    }

    // 获取DataAsset的包路径
    FString PackagePath = ConfigAsset->GetPackage()->GetName();

    // 提取目录路径
    FString PackageRoot;
    FString PackageDirectory;
    FString FileName;
    FPackageName::SplitLongPackageName(PackagePath, PackageRoot, PackageDirectory, FileName);

    // 返回完整的目录路径（包含根路径）
    return PackageRoot + PackageDirectory;
}

bool FNeoAssetDependencyResolver::IsAssetInDirectory(const FString& AssetPath, const FString& DirectoryPath)
{
    // 检查资产路径是否以目录路径开头
    return AssetPath.StartsWith(DirectoryPath);
}

bool FNeoAssetDependencyResolver::IsExcludedAsset(const FString& AssetPath)
{
    // 排除骨骼资产（会打包到其他PAK）
    if (IsSkeletonAsset(AssetPath))
    {
        return true;
    }

    // 排除插件资产
    if (IsPluginAsset(AssetPath))
    {
        return true;
    }

    // 排除引擎资产
    if (IsEngineAsset(AssetPath))
    {
        return true;
    }

    return false;
}

bool FNeoAssetDependencyResolver::IsSkeletonAsset(const FString& AssetPath)
{
    // 检查资产类型是否为USkeleton
    if (UObject* Asset = LoadObject<UObject>(nullptr, *AssetPath))
    {
        return Asset->IsA<USkeleton>();
    }
    return false;
}

bool FNeoAssetDependencyResolver::IsPluginAsset(const FString& AssetPath)
{
    // 检查路径是否以/Plugins/开头
    return AssetPath.StartsWith(TEXT("/Plugins/"));
}

bool FNeoAssetDependencyResolver::IsEngineAsset(const FString& AssetPath)
{
    // 检查路径是否以/Engine/开头
    return AssetPath.StartsWith(TEXT("/Engine/"));
}
