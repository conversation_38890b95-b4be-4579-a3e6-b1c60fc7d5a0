// Copyright Epic Games, Inc. All Rights Reserved.

#include "NeoPakToolsEditor.h"
#include "AssetTools/NeoSkeletonPakConfigActions.h"
#include "AssetTools/NeoCharacterPakConfigActions.h"
#include "AssetTools/NeoClothingPakConfigActions.h"
#include "AssetTools/NeoMapPakConfigActions.h"
#include "AssetTools/NeoAnimSequencePakConfigActions.h"
#include "AssetTools/NeoAnimMontagePakConfigActions.h"
#include "Commands/NeoPakToolsCommands.h"
#include "UI/NeoPakToolsWindowManager.h"
#include "Utils/NeoPakToolsTestUtils.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "ToolMenus.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"

#define LOCTEXT_NAMESPACE "FNeoPakToolsEditorModule"

DEFINE_LOG_CATEGORY(LogNeoPakToolsEditor);

void FNeoPakToolsEditorModule::StartupModule()
{
	UE_LOG(LogNeoPakToolsEditor, Log, TEXT("NeoPakToolsEditor module started"));

	// Initialize commands
	FNeoPakToolsCommands::Register();

	// Initialize window manager
	FNeoPakToolsWindowManager::Initialize();

	// Register asset type actions
	RegisterAssetTypeActions();

	// Register menu extensions
	RegisterMenuExtensions();
}

void FNeoPakToolsEditorModule::ShutdownModule()
{
	UE_LOG(LogNeoPakToolsEditor, Log, TEXT("NeoPakToolsEditor module shutdown"));

	// Unregister menu extensions
	UnregisterMenuExtensions();

	// Unregister asset type actions
	UnregisterAssetTypeActions();

	// Shutdown window manager
	FNeoPakToolsWindowManager::Shutdown();

	// Unregister commands
	FNeoPakToolsCommands::Unregister();
}

void FNeoPakToolsEditorModule::RegisterAssetTypeActions()
{
	IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();
	
	// Register skeleton config actions
	TSharedRef<IAssetTypeActions> SkeletonActions = MakeShareable(new FNeoSkeletonPakConfigActions);
	AssetTools.RegisterAssetTypeActions(SkeletonActions);
	CreatedAssetTypeActions.Add(SkeletonActions);
	
	// Register character config actions
	TSharedRef<IAssetTypeActions> CharacterActions = MakeShareable(new FNeoCharacterPakConfigActions);
	AssetTools.RegisterAssetTypeActions(CharacterActions);
	CreatedAssetTypeActions.Add(CharacterActions);
	
	// Register clothing config actions
	TSharedRef<IAssetTypeActions> ClothingActions = MakeShareable(new FNeoClothingPakConfigActions);
	AssetTools.RegisterAssetTypeActions(ClothingActions);
	CreatedAssetTypeActions.Add(ClothingActions);
	
	// Register map config actions
	TSharedRef<IAssetTypeActions> MapActions = MakeShareable(new FNeoMapPakConfigActions);
	AssetTools.RegisterAssetTypeActions(MapActions);
	CreatedAssetTypeActions.Add(MapActions);

	// Register animation sequence config actions
	TSharedRef<IAssetTypeActions> AnimSequenceActions = MakeShareable(new FNeoAnimSequencePakConfigActions);
	AssetTools.RegisterAssetTypeActions(AnimSequenceActions);
	CreatedAssetTypeActions.Add(AnimSequenceActions);

	// Register animation montage config actions
	TSharedRef<IAssetTypeActions> AnimMontageActions = MakeShareable(new FNeoAnimMontagePakConfigActions);
	AssetTools.RegisterAssetTypeActions(AnimMontageActions);
	CreatedAssetTypeActions.Add(AnimMontageActions);
}

void FNeoPakToolsEditorModule::UnregisterAssetTypeActions()
{
	FAssetToolsModule* AssetToolsModule = FModuleManager::GetModulePtr<FAssetToolsModule>("AssetTools");
	if (AssetToolsModule)
	{
		IAssetTools& AssetTools = AssetToolsModule->Get();
		for (int32 Index = 0; Index < CreatedAssetTypeActions.Num(); ++Index)
		{
			AssetTools.UnregisterAssetTypeActions(CreatedAssetTypeActions[Index]);
		}
	}
	CreatedAssetTypeActions.Empty();
}

void FNeoPakToolsEditorModule::RegisterMenuExtensions()
{
	// Register main menu extension
	UToolMenus* ToolMenus = UToolMenus::Get();
	if (ToolMenus)
	{
		// Add to Tools menu
		UToolMenu* ToolsMenu = ToolMenus->ExtendMenu("LevelEditor.MainMenu.Tools");
		if (ToolsMenu)
		{
			FToolMenuSection& Section = ToolsMenu->FindOrAddSection("NeoPakTools");
			Section.AddMenuEntry(
				"OpenNeoPakManager",
				LOCTEXT("OpenNeoPakManager", "NeoPak Manager"),
				LOCTEXT("OpenNeoPakManagerTooltip", "Open the NeoPakTools PAK Manager window"),
				FSlateIcon(),
				FUIAction(FExecuteAction::CreateStatic(&FNeoPakToolsWindowManager::OpenPakManagerWindow))
			);

			Section.AddSeparator("NeoPakToolsSeparator");

			FToolMenuEntry& ValidateEntry = Section.AddEntry(FToolMenuEntry::InitMenuEntry(
				"ValidateNeoPakTools",
				LOCTEXT("ValidateNeoPakTools", "Validate NeoPakTools"),
				LOCTEXT("ValidateNeoPakToolsTooltip", "Validate all NeoPakTools components"),
				FSlateIcon(),
				FUIAction(FExecuteAction::CreateStatic(&FNeoPakToolsTestUtils::ValidatePluginComponents))
			));

			FToolMenuEntry& SampleEntry = Section.AddEntry(FToolMenuEntry::InitMenuEntry(
				"CreateSampleConfigs",
				LOCTEXT("CreateSampleConfigs", "Create Sample Configurations"),
				LOCTEXT("CreateSampleConfigsTooltip", "Create sample DataAsset configurations for testing"),
				FSlateIcon(),
				FUIAction(FExecuteAction::CreateStatic(&FNeoPakToolsTestUtils::CreateSampleConfigurations))
			));
		}
	}
}

void FNeoPakToolsEditorModule::UnregisterMenuExtensions()
{
	// Menu extensions are automatically cleaned up when the module shuts down
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FNeoPakToolsEditorModule, NeoPakToolsEditor)
