// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoAnimMontagePakConfigActions.h"
#include "NeoPakToolsEditor.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Misc/MessageDialog.h"
#include "Utils/NeoAssetDependencyResolver.h"

#define LOCTEXT_NAMESPACE "NeoAnimMontagePakConfigActions"

FText FNeoAnimMontagePakConfigActions::GetName() const
{
    return LOCTEXT("AssetTypeActions_NeoAnimMontagePakConfig", "Animation Montage Pack Config");
}

FColor FNeoAnimMontagePakConfigActions::GetTypeColor() const
{
    return FColor(64, 64, 255); // Dark Blue
}

UClass* FNeoAnimMontagePakConfigActions::GetSupportedClass() const
{
    return UNeoAnimMontagePakConfig::StaticClass();
}

uint32 FNeoAnimMontagePakConfigActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FNeoAnimMontagePakConfigActions::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> AnimMontageConfigs = GetTypedWeakObjectPtrs<UNeoAnimMontagePakConfig>(InObjects);

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimMontagePakConfig_ExecutePackaging", "Execute Packaging"),
        LOCTEXT("AnimMontagePakConfig_ExecutePackagingTooltip", "Execute the packaging process for this animation montage configuration"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::ExecutePackaging, AnimMontageConfigs),
            FCanExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::CanExecutePackaging, AnimMontageConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimMontagePakConfig_ValidateConfig", "Validate Configuration"),
        LOCTEXT("AnimMontagePakConfig_ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::ExecuteValidateConfig, AnimMontageConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimMontagePakConfig_CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("AnimMontagePakConfig_CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::ExecuteCheckDirectoryDependencies, AnimMontageConfigs)
        )
    );
}

bool FNeoAnimMontagePakConfigActions::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects) const
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            if (!Config->ValidateConfiguration())
            {
                return false;
            }
        }
    }
    return true;
}

void FNeoAnimMontagePakConfigActions::ExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for animation montage config: %s"), *Config->GetName());
            
            if (Config->ExecutePackaging())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingSuccess", "Packaging completed successfully: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingFailed", "Packaging failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoAnimMontagePakConfigActions::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating animation montage config: %s"), *Config->GetName());
            
            if (Config->ValidateConfiguration())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationSuccess", "Configuration is valid: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationFailed", "Configuration validation failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoAnimMontagePakConfigActions::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects)
{
    FNeoAssetDependencyResolver DependencyResolver;
    int32 PassedCount = 0;
    int32 FailedCount = 0;
    TArray<FString> AllErrorMessages;

    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking directory dependencies for animation montage config: %s"), *Config->GetName());

            // 执行依赖检查
            FNeoDependencyCheckResult Result = DependencyResolver.CheckDataAssetDirectoryDependencies(Config, false);

            if (Result.bCheckPassed)
            {
                PassedCount++;
                UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Dependency check passed for: %s"), *Config->ConfigName);
            }
            else
            {
                FailedCount++;
                UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Dependency check failed for: %s"), *Config->ConfigName);

                // 收集错误信息
                for (const FString& ErrorMsg : Result.ErrorMessages)
                {
                    AllErrorMessages.Add(FString::Printf(TEXT("[%s] %s"), *Config->ConfigName, *ErrorMsg));
                }
            }
        }
    }

    // 显示详细结果
    FString DetailedMessage = FString::Printf(TEXT("Animation Montage Dependency Check Results:\n\nPassed: %d\nFailed: %d\n"),
                                             PassedCount, FailedCount);

    if (AllErrorMessages.Num() > 0)
    {
        DetailedMessage += TEXT("\nDetailed Errors:\n");
        for (int32 i = 0; i < FMath::Min(AllErrorMessages.Num(), 20); ++i)
        {
            DetailedMessage += FString::Printf(TEXT("• %s\n"), *AllErrorMessages[i]);
        }

        if (AllErrorMessages.Num() > 20)
        {
            DetailedMessage += FString::Printf(TEXT("... and %d more errors\n"), AllErrorMessages.Num() - 20);
        }
    }

    FMessageDialog::Open(
        FailedCount > 0 ? EAppMsgType::Ok : EAppMsgType::Ok,
        FText::FromString(DetailedMessage),
        LOCTEXT("DependencyCheckResultTitle", "Directory Dependency Check Results")
    );
}

#undef LOCTEXT_NAMESPACE
