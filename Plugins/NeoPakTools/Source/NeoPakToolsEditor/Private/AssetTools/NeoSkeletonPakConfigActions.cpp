// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoSkeletonPakConfigActions.h"

#define LOCTEXT_NAMESPACE "NeoSkeletonPakConfigActions"

UClass* FNeoSkeletonPakConfigActions::GetSupportedClass() const
{
    return UNeoSkeletonPakConfig::StaticClass();
}

FColor FNeoSkeletonPakConfigActions::GetAssetTypeColor() const
{
    return FColor(255, 196, 128); // Orange
}

FText FNeoSkeletonPakConfigActions::GetAssetTypeName() const
{
    return LOCTEXT("AssetTypeActions_NeoSkeletonPakConfig", "Skeleton Pack Config");
}

#undef LOCTEXT_NAMESPACE
