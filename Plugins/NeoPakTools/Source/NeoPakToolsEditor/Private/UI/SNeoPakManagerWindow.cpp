// Copyright Epic Games, Inc. All Rights Reserved.

#include "UI/SNeoPakManagerWindow.h"
#include "UI/SNeoPakInfoWidget.h"
#include "UI/SNeoPakProgressDialog.h"
#include "NeoPakToolsEditor.h"
#include "ISettingsModule.h"
#include "Config/NeoPakToolsSettings.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Views/STableRow.h"
#include "Widgets/Layout/SSeparator.h"
#include "Widgets/Layout/SSplitter.h"
#include "Widgets/Notifications/SProgressBar.h"
#include "Styling/AppStyle.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/Engine.h"

#define LOCTEXT_NAMESPACE "SNeoPakManagerWindow"

void SNeoPakManagerWindow::Construct(const FArguments& InArgs)
{
    PakManager = UNeoPakManager::GetInstance();

    ChildSlot
    [
        SNew(SVerticalBox)
        
        // Toolbar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            CreateToolbar()
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SNew(SSeparator)
        ]

        // Main content area
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(5.0f)
        [
            SNew(SSplitter)
            .Orientation(Orient_Horizontal)

            // Left panel - Configuration list
            + SSplitter::Slot()
            .Value(0.3f)
            [
                CreateConfigListPanel()
            ]

            // Middle panel - PAK list
            + SSplitter::Slot()
            .Value(0.3f)
            [
                CreatePakListPanel()
            ]

            // Right panel - Info
            + SSplitter::Slot()
            .Value(0.4f)
            [
                CreateInfoPanel()
            ]
        ]

        // Status bar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            CreateStatusBar()
        ]
    ];

    RefreshAll();
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateToolbar()
{
    return SNew(SHorizontalBox)

        // Package Selected button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(PackageSelectedButton, SButton)
            .Text(LOCTEXT("PackageSelected", "Package Selected"))
            .ToolTipText(LOCTEXT("PackageSelectedTooltip", "Package the selected configuration"))
            .OnClicked(this, &SNeoPakManagerWindow::OnPackageSelectedClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanPackageSelected)
        ]

        // Package All button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(PackageAllButton, SButton)
            .Text(LOCTEXT("PackageAll", "Package All"))
            .ToolTipText(LOCTEXT("PackageAllTooltip", "Package all valid configurations"))
            .OnClicked(this, &SNeoPakManagerWindow::OnPackageAllClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanPackageAll)
        ]

        // Validate All button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(ValidateAllButton, SButton)
            .Text(LOCTEXT("ValidateAll", "Validate All"))
            .ToolTipText(LOCTEXT("ValidateAllTooltip", "Validate all configurations"))
            .OnClicked(this, &SNeoPakManagerWindow::OnValidateAllClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanValidateAll)
        ]

        // Spacer
        + SHorizontalBox::Slot()
        .FillWidth(1.0f)
        [
            SNullWidget::NullWidget
        ]

        // Clean Output button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(CleanOutputButton, SButton)
            .Text(LOCTEXT("CleanOutput", "Clean Output"))
            .ToolTipText(LOCTEXT("CleanOutputTooltip", "Clean the PAK output directory"))
            .OnClicked(this, &SNeoPakManagerWindow::OnCleanOutputClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanCleanOutput)
        ]

        // Refresh button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(RefreshButton, SButton)
            .Text(LOCTEXT("Refresh", "Refresh"))
            .ToolTipText(LOCTEXT("RefreshTooltip", "Refresh all lists"))
            .OnClicked(this, &SNeoPakManagerWindow::OnRefreshClicked)
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateConfigListPanel()
{
    return SNew(SVerticalBox)

        // Header
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("ConfigurationsHeader", "DataAsset Configurations"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
        ]

        // List view
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(5.0f)
        [
            SAssignNew(ConfigListView, SListView<TSharedPtr<UNeoPakConfigAssetBase*>>)
            .ListItemsSource(&ConfigAssets)
            .OnGenerateRow(this, &SNeoPakManagerWindow::OnGenerateConfigRow)
            .OnSelectionChanged(this, &SNeoPakManagerWindow::OnConfigSelectionChanged)
            .SelectionMode(ESelectionMode::Multi)
        ]

        // 批量操作按钮
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 5.0f, 0.0f)
            [
                SNew(SButton)
                .Text(LOCTEXT("BatchPackageButton", "Batch Package"))
                .OnClicked(this, &SNeoPakManagerWindow::OnBatchPackageClicked)
                .IsEnabled(this, &SNeoPakManagerWindow::CanBatchPackage)
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 5.0f, 0.0f)
            [
                SNew(SButton)
                .Text(LOCTEXT("BatchValidateButton", "Batch Validate"))
                .OnClicked(this, &SNeoPakManagerWindow::OnBatchValidateClicked)
                .IsEnabled(this, &SNeoPakManagerWindow::CanBatchValidate)
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 5.0f, 0.0f)
            [
                SNew(SButton)
                .Text(LOCTEXT("CleanOutputButton", "Clean Output"))
                .OnClicked(this, &SNeoPakManagerWindow::OnCleanOutputClicked)
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 5.0f, 0.0f)
            [
                SNew(SButton)
                .Text(LOCTEXT("CheckDependenciesButton", "Check Dependencies"))
                .OnClicked(this, &SNeoPakManagerWindow::OnCheckDependenciesClicked)
                .IsEnabled(this, &SNeoPakManagerWindow::CanCheckDependencies)
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            [
                SNew(SButton)
                .Text(LOCTEXT("AdvancedOptionsButton", "Advanced Options"))
                .OnClicked(this, &SNeoPakManagerWindow::OnAdvancedOptionsClicked)
            ]
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreatePakListPanel()
{
    return SNew(SVerticalBox)

        // Header
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("PakFilesHeader", "PAK Files"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
        ]

        // List view
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(5.0f)
        [
            SAssignNew(PakListView, SListView<TSharedPtr<FNeoPakEntry>>)
            .ListItemsSource(&PakEntries)
            .OnGenerateRow(this, &SNeoPakManagerWindow::OnGeneratePakRow)
            .OnSelectionChanged(this, &SNeoPakManagerWindow::OnPakSelectionChanged)
            .SelectionMode(ESelectionMode::Single)
        ]

        // PAK操作按钮
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 5.0f, 0.0f)
            [
                SNew(SButton)
                .Text(LOCTEXT("LoadPakButton", "Load PAK"))
                .OnClicked(this, &SNeoPakManagerWindow::OnLoadPakClicked)
                .IsEnabled(this, &SNeoPakManagerWindow::CanLoadPak)
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 5.0f, 0.0f)
            [
                SNew(SButton)
                .Text(LOCTEXT("UnloadPakButton", "Unload PAK"))
                .OnClicked(this, &SNeoPakManagerWindow::OnUnloadPakClicked)
                .IsEnabled(this, &SNeoPakManagerWindow::CanUnloadPak)
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            [
                SNew(SButton)
                .Text(LOCTEXT("RefreshPakListButton", "Refresh"))
                .OnClicked(this, &SNeoPakManagerWindow::OnRefreshPakListClicked)
            ]
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateInfoPanel()
{
    return SNew(SVerticalBox)

        // Header
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("InfoHeader", "Information"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
        ]

        // Info widget
        + SVerticalBox::Slot()
        .FillHeight(0.5f)
        .Padding(5.0f)
        [
            SAssignNew(PakInfoWidget, SNeoPakInfoWidget)
        ]

        // PAK file contents
        + SVerticalBox::Slot()
        .FillHeight(0.5f)
        .Padding(5.0f)
        [
            CreatePakFileContentsPanel()
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateStatusBar()
{
    return SNew(SVerticalBox)

        // Progress bar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SAssignNew(ProgressBar, SProgressBar)
            .Percent(this, &SNeoPakManagerWindow::GetProgressPercent)
            .Visibility(EVisibility::Collapsed)
        ]

        // Status text
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SAssignNew(StatusText, STextBlock)
            .Text(this, &SNeoPakManagerWindow::GetStatusText)
        ];
}

void SNeoPakManagerWindow::RefreshAll()
{
    RefreshConfigList();
    RefreshPakList();
    UpdateButtonStates();
    UpdateStatusText(LOCTEXT("Ready", "Ready"));
}

TSharedRef<ITableRow> SNeoPakManagerWindow::OnGenerateConfigRow(TSharedPtr<UNeoPakConfigAssetBase*> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    UNeoPakConfigAssetBase* Config = Item.IsValid() ? *Item : nullptr;
    FString DisplayName = Config ? Config->ConfigName : TEXT("Invalid Config");

    return SNew(STableRow<TSharedPtr<UNeoPakConfigAssetBase*>>, OwnerTable)
        [
            SNew(STextBlock)
            .Text(FText::FromString(DisplayName))
            .ToolTipText(Config ? FText::FromString(Config->GetFullOutputPath()) : FText::GetEmpty())
        ];
}

TSharedRef<ITableRow> SNeoPakManagerWindow::OnGeneratePakRow(TSharedPtr<FNeoPakEntry> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    FString DisplayName = Item.IsValid() ? FPaths::GetCleanFilename(Item->PakFilePath) : TEXT("Invalid PAK");

    return SNew(STableRow<TSharedPtr<FNeoPakEntry>>, OwnerTable)
        [
            SNew(SHorizontalBox)

            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SNew(STextBlock)
                .Text(FText::FromString(DisplayName))
                .ToolTipText(Item.IsValid() ? FText::FromString(Item->PakFilePath) : FText::GetEmpty())
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(5.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(Item.IsValid() ?
                    (Item->LoadStatus == EPakLoadStatus::Loaded ? LOCTEXT("Loaded", "Loaded") : LOCTEXT("NotLoaded", "Not Loaded")) :
                    FText::GetEmpty())
                .ColorAndOpacity(Item.IsValid() && Item->LoadStatus == EPakLoadStatus::Loaded ?
                    FSlateColor(FLinearColor::Green) : FSlateColor(FLinearColor::Gray))
            ]
        ];
}

void SNeoPakManagerWindow::OnConfigSelectionChanged(TSharedPtr<UNeoPakConfigAssetBase*> SelectedItem, ESelectInfo::Type SelectInfo)
{
    SelectedConfig = SelectedItem;
    UpdateButtonStates();

    // Update info panel
    if (SelectedItem.IsValid() && *SelectedItem)
    {
        FString PakPath = (*SelectedItem)->GetFullOutputPath();
        if (PakInfoWidget.IsValid())
        {
            PakInfoWidget->SetPakFilePath(PakPath);
        }
    }
}

void SNeoPakManagerWindow::OnPakSelectionChanged(TSharedPtr<FNeoPakEntry> SelectedItem, ESelectInfo::Type SelectInfo)
{
    SelectedPakEntry = SelectedItem;

    // Update info panel
    if (SelectedItem.IsValid() && PakInfoWidget.IsValid())
    {
        PakInfoWidget->SetPakFilePath(SelectedItem->PakFilePath);
    }

    // Refresh PAK file contents
    RefreshPakFileContents();
}

FReply SNeoPakManagerWindow::OnPackageSelectedClicked()
{
    if (SelectedConfig.IsValid() && *SelectedConfig && PakManager.IsValid())
    {
        UNeoPakConfigAssetBase* Config = *SelectedConfig;
        UpdateStatusText(FText::Format(LOCTEXT("PackagingConfig", "Packaging: {0}"), FText::FromString(Config->ConfigName)));
        ShowProgressBar(true);

        bool bSuccess = PakManager->PackageFromConfigWithDependencyCheck(Config, false);

        ShowProgressBar(false);
        UpdateStatusText(bSuccess ?
            FText::Format(LOCTEXT("PackagingSuccess", "Successfully packaged: {0}"), FText::FromString(Config->ConfigName)) :
            FText::Format(LOCTEXT("PackagingFailed", "Failed to package: {0}"), FText::FromString(Config->ConfigName)));

        RefreshPakList();
    }

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnPackageAllClicked()
{
    if (PakManager.IsValid())
    {
        UpdateStatusText(LOCTEXT("PackagingAll", "Packaging all configurations..."));
        ShowProgressBar(true);

        int32 SuccessCount = 0;
        int32 TotalCount = 0;

        for (const auto& ConfigPtr : ConfigAssets)
        {
            if (ConfigPtr.IsValid() && *ConfigPtr)
            {
                TotalCount++;
                UNeoPakConfigAssetBase* Config = *ConfigPtr;

                if (PakManager->PackageFromConfigWithDependencyCheck(Config, false))
                {
                    SuccessCount++;
                }
            }
        }

        ShowProgressBar(false);
        UpdateStatusText(FText::Format(LOCTEXT("PackagingAllResult", "Packaged {0}/{1} configurations"),
            FText::AsNumber(SuccessCount), FText::AsNumber(TotalCount)));

        RefreshPakList();
    }

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnValidateAllClicked()
{
    UpdateStatusText(LOCTEXT("ValidatingAll", "Validating all configurations..."));

    int32 ValidCount = 0;
    int32 TotalCount = 0;

    for (const auto& ConfigPtr : ConfigAssets)
    {
        if (ConfigPtr.IsValid() && *ConfigPtr)
        {
            TotalCount++;
            UNeoPakConfigAssetBase* Config = *ConfigPtr;

#if WITH_EDITOR
            if (Config->ValidateConfiguration())
            {
                ValidCount++;
            }
#else
            ValidCount++; // In non-editor builds, assume valid
#endif
        }
    }

    UpdateStatusText(FText::Format(LOCTEXT("ValidationResult", "Validation complete: {0}/{1} valid"),
        FText::AsNumber(ValidCount), FText::AsNumber(TotalCount)));

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnRefreshClicked()
{
    RefreshAll();
    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnCleanOutputClicked()
{
    const UNeoPakToolsSettings* Settings = UNeoPakToolsSettings::GetNeoPakToolsSettings();
    if (Settings)
    {
        FString OutputDir = Settings->DefaultOutputDirectory.Path;
        if (!OutputDir.IsEmpty())
        {
            UpdateStatusText(FText::Format(LOCTEXT("CleaningOutput", "Cleaning output directory: {0}"), FText::FromString(OutputDir)));

            // TODO: Implement actual directory cleaning
            // For now, just show a message
            UpdateStatusText(LOCTEXT("CleaningComplete", "Output directory cleaning completed"));
        }
        else
        {
            UpdateStatusText(LOCTEXT("NoOutputDir", "No output directory specified in settings"));
        }
    }

    return FReply::Handled();
}

void SNeoPakManagerWindow::RefreshConfigList()
{
    ConfigAssets.Empty();

    // Find all DataAsset configurations in the project
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    TArray<FAssetData> AssetDataList;
    AssetRegistry.GetAssetsByClass(UNeoPakConfigAssetBase::StaticClass()->GetClassPathName(), AssetDataList, true);

    for (const FAssetData& AssetData : AssetDataList)
    {
        if (UNeoPakConfigAssetBase* Config = Cast<UNeoPakConfigAssetBase>(AssetData.GetAsset()))
        {
            ConfigAssets.Add(MakeShareable(new UNeoPakConfigAssetBase*(Config)));
        }
    }

    if (ConfigListView.IsValid())
    {
        ConfigListView->RequestListRefresh();
    }
}

void SNeoPakManagerWindow::RefreshPakList()
{
    PakEntries.Empty();

    if (PakManager.IsValid())
    {
        // 首先扫描PAK文件
        PakManager->ScanForPakFiles();

        // 然后获取所有发现的PAK文件
        TArray<FNeoPakEntry> DiscoveredPaks = PakManager->GetLoadedPaks();
        for (const FNeoPakEntry& Entry : DiscoveredPaks)
        {
            PakEntries.Add(MakeShareable(new FNeoPakEntry(Entry)));
        }

        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Refreshed PAK list: found %d PAK files"), PakEntries.Num());
    }

    if (PakListView.IsValid())
    {
        PakListView->RequestListRefresh();
    }
}

void SNeoPakManagerWindow::UpdateButtonStates()
{
    // Update button enabled states based on current selection and state
}

void SNeoPakManagerWindow::UpdateStatusText(const FText& NewStatus)
{
    if (StatusText.IsValid())
    {
        StatusText->SetText(NewStatus);
    }
}

void SNeoPakManagerWindow::ShowProgressBar(bool bShow)
{
    if (ProgressBar.IsValid())
    {
        ProgressBar->SetVisibility(bShow ? EVisibility::Visible : EVisibility::Collapsed);
    }
}

bool SNeoPakManagerWindow::CanPackageSelected() const
{
    return SelectedConfig.IsValid() && *SelectedConfig;
}

bool SNeoPakManagerWindow::CanPackageAll() const
{
    return ConfigAssets.Num() > 0;
}

bool SNeoPakManagerWindow::CanValidateAll() const
{
    return ConfigAssets.Num() > 0;
}

bool SNeoPakManagerWindow::CanCleanOutput() const
{
    const UNeoPakToolsSettings* Settings = UNeoPakToolsSettings::GetNeoPakToolsSettings();
    return Settings && !Settings->DefaultOutputDirectory.Path.IsEmpty();
}

FText SNeoPakManagerWindow::GetSelectedConfigText() const
{
    if (SelectedConfig.IsValid() && *SelectedConfig)
    {
        return FText::FromString((*SelectedConfig)->ConfigName);
    }
    return LOCTEXT("NoConfigSelected", "No configuration selected");
}

FText SNeoPakManagerWindow::GetSelectedPakText() const
{
    if (SelectedPakEntry.IsValid())
    {
        return FText::FromString(FPaths::GetCleanFilename(SelectedPakEntry->PakFilePath));
    }
    return LOCTEXT("NoPakSelected", "No PAK selected");
}

FText SNeoPakManagerWindow::GetStatusText() const
{
    return LOCTEXT("Ready", "Ready");
}

FReply SNeoPakManagerWindow::OnLoadPakClicked()
{
    if (SelectedPakEntry.IsValid() && PakManager.IsValid())
    {
        bool bSuccess = PakManager->LoadPakFile(SelectedPakEntry->PakFilePath);
        if (bSuccess)
        {
            // 刷新列表以更新状态
            RefreshPakList();
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Successfully loaded PAK: %s"), *SelectedPakEntry->PakFilePath);
        }
        else
        {
            UE_LOG(LogNeoPakToolsEditor, Error, TEXT("Failed to load PAK: %s"), *SelectedPakEntry->PakFilePath);
        }
    }

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnUnloadPakClicked()
{
    if (SelectedPakEntry.IsValid() && PakManager.IsValid())
    {
        bool bSuccess = PakManager->UnloadPakFile(SelectedPakEntry->PakFilePath);
        if (bSuccess)
        {
            // 刷新列表以更新状态
            RefreshPakList();
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Successfully unloaded PAK: %s"), *SelectedPakEntry->PakFilePath);
        }
        else
        {
            UE_LOG(LogNeoPakToolsEditor, Error, TEXT("Failed to unload PAK: %s"), *SelectedPakEntry->PakFilePath);
        }
    }

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnRefreshPakListClicked()
{
    RefreshPakList();
    return FReply::Handled();
}

bool SNeoPakManagerWindow::CanLoadPak() const
{
    if (!SelectedPakEntry.IsValid() || !PakManager.IsValid())
    {
        return false;
    }

    // 只有未加载的PAK文件才能加载
    EPakLoadStatus Status = PakManager->GetPakLoadStatus(SelectedPakEntry->PakFilePath);
    return Status == EPakLoadStatus::NotLoaded;
}

bool SNeoPakManagerWindow::CanUnloadPak() const
{
    if (!SelectedPakEntry.IsValid() || !PakManager.IsValid())
    {
        return false;
    }

    // 只有已加载的PAK文件才能卸载
    EPakLoadStatus Status = PakManager->GetPakLoadStatus(SelectedPakEntry->PakFilePath);
    return Status == EPakLoadStatus::Loaded;
}

FReply SNeoPakManagerWindow::OnBatchPackageClicked()
{
    if (!PakManager.IsValid())
    {
        return FReply::Handled();
    }

    // 获取选中的配置
    TArray<TSharedPtr<UNeoPakConfigAssetBase*>> SelectedConfigs = ConfigListView->GetSelectedItems();

    if (SelectedConfigs.Num() == 0)
    {
        // 如果没有选中任何配置，使用所有配置
        TArray<UNeoPakConfigAssetBase*> AllConfigs = PakManager->GetAllConfigAssets();

        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("No configs selected, packaging all %d configs"), AllConfigs.Num());

        // 显示进度对话框
        TSharedPtr<SWindow> ProgressWindow = SNeoPakProgressDialog::ShowProgressDialog(
            PakManager,
            FString::Printf(TEXT("Batch Packaging (All %d configs)"), AllConfigs.Num())
        );

        bool bSuccess = PakManager->BatchPackageConfigs(AllConfigs, true);
        if (bSuccess)
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Batch packaging completed successfully"));
        }
        else
        {
            UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Batch packaging completed with some failures"));
        }
    }
    else
    {
        // 使用选中的配置
        TArray<UNeoPakConfigAssetBase*> ConfigsToPackage;
        for (const auto& SelectedConfigItem : SelectedConfigs)
        {
            if (SelectedConfigItem.IsValid() && *SelectedConfigItem)
            {
                ConfigsToPackage.Add(*SelectedConfigItem);
            }
        }

        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Packaging %d selected configs"), ConfigsToPackage.Num());

        // 显示进度对话框
        TSharedPtr<SWindow> ProgressWindow = SNeoPakProgressDialog::ShowProgressDialog(
            PakManager,
            FString::Printf(TEXT("Batch Packaging (%d configs)"), ConfigsToPackage.Num())
        );

        bool bSuccess = PakManager->BatchPackageConfigs(ConfigsToPackage, true);
        if (bSuccess)
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Batch packaging of selected configs completed successfully"));
        }
        else
        {
            UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Batch packaging of selected configs completed with some failures"));
        }
    }

    // 刷新PAK列表以显示新创建的PAK文件
    RefreshPakList();

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnBatchValidateClicked()
{
    if (!PakManager.IsValid())
    {
        return FReply::Handled();
    }

    // 获取选中的配置
    TArray<TSharedPtr<UNeoPakConfigAssetBase*>> SelectedConfigs = ConfigListView->GetSelectedItems();

    TArray<UNeoPakConfigAssetBase*> ConfigsToValidate;

    if (SelectedConfigs.Num() == 0)
    {
        // 如果没有选中任何配置，使用所有配置
        ConfigsToValidate = PakManager->GetAllConfigAssets();
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("No configs selected, validating all %d configs"), ConfigsToValidate.Num());
    }
    else
    {
        // 使用选中的配置
        for (const auto& SelectedConfigItem : SelectedConfigs)
        {
            if (SelectedConfigItem.IsValid() && *SelectedConfigItem)
            {
                ConfigsToValidate.Add(*SelectedConfigItem);
            }
        }
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating %d selected configs"), ConfigsToValidate.Num());
    }

    // 执行批量验证
    TArray<FString> ValidationErrors = PakManager->BatchValidateConfigs(ConfigsToValidate);

    if (ValidationErrors.Num() == 0)
    {
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("All configurations passed validation"));
    }
    else
    {
        UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Validation completed with %d errors:"), ValidationErrors.Num());
        for (const FString& Error : ValidationErrors)
        {
            UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("  - %s"), *Error);
        }
    }

    return FReply::Handled();
}



bool SNeoPakManagerWindow::CanBatchPackage() const
{
    return PakManager.IsValid();
}

bool SNeoPakManagerWindow::CanBatchValidate() const
{
    return PakManager.IsValid();
}

FReply SNeoPakManagerWindow::OnCheckDependenciesClicked()
{
    if (!PakManager.IsValid())
    {
        return FReply::Handled();
    }

    // 获取选中的配置
    TArray<TSharedPtr<UNeoPakConfigAssetBase*>> SelectedConfigs = ConfigListView->GetSelectedItems();

    if (SelectedConfigs.Num() == 0)
    {
        UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("No configurations selected for dependency check"));
        return FReply::Handled();
    }

    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking dependencies for %d selected configurations"), SelectedConfigs.Num());

    int32 TotalIssues = 0;

    for (const auto& SelectedConfigItem : SelectedConfigs)
    {
        if (SelectedConfigItem.IsValid() && *SelectedConfigItem)
        {
            UNeoPakConfigAssetBase* Config = *SelectedConfigItem;
            FString ConfigName = Config->GetName();

            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking dependencies for config: %s"), *ConfigName);

            // 执行依赖检查
            FNeoDependencyCheckResult DependencyResult = PakManager->CheckDependencies(Config);

            if (DependencyResult.bCheckPassed)
            {
                UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Dependencies check passed for: %s"), *ConfigName);
            }
            else
            {
                UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Dependencies check failed for: %s"), *ConfigName);
                UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Issues found: %d"), DependencyResult.Issues.Num());

                for (const FString& Issue : DependencyResult.Issues)
                {
                    UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("  - %s"), *Issue);
                }

                TotalIssues += DependencyResult.Issues.Num();

                // 询问是否尝试自动修复
                UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Attempting auto-fix for config: %s"), *ConfigName);

                bool bAutoFixSuccess = PakManager->PackageFromConfigWithDependencyCheck(Config, true);

                if (bAutoFixSuccess)
                {
                    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Auto-fix successful for: %s"), *ConfigName);
                }
                else
                {
                    UE_LOG(LogNeoPakToolsEditor, Error, TEXT("Auto-fix failed for: %s"), *ConfigName);
                }
            }
        }
    }

    if (TotalIssues == 0)
    {
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("All selected configurations passed dependency checks"));
    }
    else
    {
        UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Dependency check completed with %d total issues"), TotalIssues);
    }

    return FReply::Handled();
}

bool SNeoPakManagerWindow::CanCheckDependencies() const
{
    if (!PakManager.IsValid())
    {
        return false;
    }

    // 需要至少选中一个配置
    TArray<TSharedPtr<UNeoPakConfigAssetBase*>> SelectedConfigs = ConfigListView->GetSelectedItems();
    return SelectedConfigs.Num() > 0;
}

FReply SNeoPakManagerWindow::OnAdvancedOptionsClicked()
{
    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Opening advanced packaging options"));

    // 打开项目设置到NeoPakTools部分
    FModuleManager::LoadModuleChecked<ISettingsModule>("Settings").ShowViewer("Project", "Plugins", "NeoPakTools");

    return FReply::Handled();
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreatePakFileContentsPanel()
{
    return SNew(SVerticalBox)

        // Header
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("PakContentsHeader", "PAK File Contents"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
        ]

        // File list
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(2.0f)
        [
            SNew(SBorder)
            .BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
            [
                SAssignNew(PakFileListView, SListView<TSharedPtr<FString>>)
                .ListItemsSource(&PakFileContents)
                .OnGenerateRow(this, &SNeoPakManagerWindow::OnGeneratePakFileRow)
                .SelectionMode(ESelectionMode::Single)
                .HeaderRow
                (
                    SNew(SHeaderRow)
                    + SHeaderRow::Column("FileName")
                    .DefaultLabel(LOCTEXT("FileNameColumn", "File Name"))
                    .FillWidth(1.0f)
                )
            ]
        ];
}

TSharedRef<ITableRow> SNeoPakManagerWindow::OnGeneratePakFileRow(TSharedPtr<FString> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    return SNew(STableRow<TSharedPtr<FString>>, OwnerTable)
        [
            SNew(SHorizontalBox)

            // File name
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            .VAlign(VAlign_Center)
            .Padding(4.0f, 2.0f)
            [
                SNew(STextBlock)
                .Text(Item.IsValid() ? FText::FromString(*Item) : FText::GetEmpty())
                .ToolTipText(Item.IsValid() ? FText::FromString(*Item) : FText::GetEmpty())
            ]
        ];
}

void SNeoPakManagerWindow::RefreshPakFileContents()
{
    PakFileContents.Empty();

    if (SelectedPakEntry.IsValid() && PakManager.IsValid())
    {
        TArray<FString> FileList = PakManager->GetPakFileContents(SelectedPakEntry->PakFilePath);

        for (const FString& FileName : FileList)
        {
            PakFileContents.Add(MakeShareable(new FString(FileName)));
        }

        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Refreshed PAK file contents: %d files"), PakFileContents.Num());
    }

    if (PakFileListView.IsValid())
    {
        PakFileListView->RequestListRefresh();
    }
}

TOptional<float> SNeoPakManagerWindow::GetProgressPercent() const
{
    // For now, return indeterminate progress
    return TOptional<float>();
}

#undef LOCTEXT_NAMESPACE
