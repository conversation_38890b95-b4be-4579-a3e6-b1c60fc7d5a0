#include "UI/SNeoPakResultDialog.h"
#include "NeoPakToolsEditor.h"
#include "Widgets/SWindow.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Views/STableRow.h"
#include "Framework/Application/SlateApplication.h"
#include "Styling/AppStyle.h"

#define LOCTEXT_NAMESPACE "SNeoPakResultDialog"

void SNeoPakResultDialog::Construct(const FArguments& InArgs)
{
    Title = InArgs._Title;
    SuccessCount = InArgs._SuccessCount;
    FailureCount = InArgs._FailureCount;
    Summary = InArgs._Summary;

    // 转换失败项目为共享指针
    FailedItems.Empty();
    for (const FString& Item : InArgs._FailedItems)
    {
        FailedItems.Add(MakeShareable(new FString(Item)));
    }

    ChildSlot
    [
        SNew(SBorder)
        .BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
        .Padding(15.0f)
        [
            SNew(SVerticalBox)

            // 标题和图标
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(0.0f, 0.0f, 0.0f, 15.0f)
            [
                SNew(SHorizontalBox)

                // 结果图标
                + SHorizontalBox::Slot()
                .AutoWidth()
                .VAlign(VAlign_Center)
                .Padding(0.0f, 0.0f, 10.0f, 0.0f)
                [
                    SNew(SImage)
                    .Image(this, &SNeoPakResultDialog::GetResultIcon)
                    .ColorAndOpacity(this, &SNeoPakResultDialog::GetResultColor)
                ]

                // 标题文本
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
                .VAlign(VAlign_Center)
                [
                    SNew(STextBlock)
                    .Text(FText::FromString(Title))
                    .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
                ]
            ]

            // 结果统计
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(0.0f, 0.0f, 0.0f, 10.0f)
            [
                SNew(STextBlock)
                .Text(this, &SNeoPakResultDialog::GetResultText)
                .Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
            ]

            // 摘要信息（如果有）
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(0.0f, 0.0f, 0.0f, 10.0f)
            [
                SNew(STextBlock)
                .Text(this, &SNeoPakResultDialog::GetSummaryText)
                .AutoWrapText(true)
                .Visibility(Summary.IsEmpty() ? EVisibility::Collapsed : EVisibility::Visible)
            ]

            // 失败项目列表（如果有）
            + SVerticalBox::Slot()
            .FillHeight(1.0f)
            .Padding(0.0f, 0.0f, 0.0f, 15.0f)
            [
                SNew(SVerticalBox)
                .Visibility(FailedItems.Num() > 0 ? EVisibility::Visible : EVisibility::Collapsed)

                + SVerticalBox::Slot()
                .AutoHeight()
                .Padding(0.0f, 0.0f, 0.0f, 5.0f)
                [
                    SNew(STextBlock)
                    .Text(LOCTEXT("FailedItemsLabel", "Failed Items:"))
                    .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
                ]

                + SVerticalBox::Slot()
                .FillHeight(1.0f)
                [
                    SNew(SBorder)
                    .BorderImage(FAppStyle::GetBrush("ToolPanel.DarkGroupBorder"))
                    .Padding(5.0f)
                    [
                        SNew(SBox)
                        .MaxDesiredHeight(200.0f)
                        [
                            SAssignNew(FailedItemsListView, SListView<TSharedPtr<FString> >)
                            .ListItemsSource(&FailedItems)
                            .OnGenerateRow(this, &SNeoPakResultDialog::OnGenerateFailedItemRow)
                            .SelectionMode(ESelectionMode::None)
                        ]
                    ]
                ]
            ]

            // 确定按钮
            + SVerticalBox::Slot()
            .AutoHeight()
            [
                SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)

                + SHorizontalBox::Slot()
                .AutoWidth()
                [
                    SNew(SButton)
                    .Text(LOCTEXT("OKButton", "OK"))
                    .OnClicked(this, &SNeoPakResultDialog::OnOKClicked)
                    .HAlign(HAlign_Center)
                    .VAlign(VAlign_Center)
                    .MinDesiredWidth(80.0f)
                ]

                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
            ]
        ]
    ];
}

void SNeoPakResultDialog::ShowResultDialog(
    const FString& Title,
    int32 SuccessCount,
    int32 FailureCount,
    const TArray<FString>& FailedItems,
    const FString& Summary)
{
    TSharedRef<SNeoPakResultDialog> ResultDialog = SNew(SNeoPakResultDialog)
        .Title(Title)
        .SuccessCount(SuccessCount)
        .FailureCount(FailureCount)
        .FailedItems(FailedItems)
        .Summary(Summary);

    TSharedPtr<SWindow> Window = SNew(SWindow)
        .Title(FText::FromString(FString::Printf(TEXT("NeoPakTools - %s"), *Title)))
        .SizingRule(ESizingRule::UserSized)
        .ClientSize(FVector2D(500, FailedItems.Num() > 0 ? 400 : 250))
        .SupportsMaximize(false)
        .SupportsMinimize(false)
        [
            ResultDialog
        ];

    ResultDialog->DialogWindow = Window;

    FSlateApplication::Get().AddModalWindow(Window.ToSharedRef(), FSlateApplication::Get().GetActiveTopLevelWindow());
}

FReply SNeoPakResultDialog::OnOKClicked()
{
    if (DialogWindow.IsValid())
    {
        DialogWindow.Pin()->RequestDestroyWindow();
    }

    return FReply::Handled();
}

TSharedRef<ITableRow<TSharedPtr<FString> > > SNeoPakResultDialog::OnGenerateFailedItemRow(TSharedPtr<FString> Item, const TSharedRef<STableViewBase<TSharedPtr<FString> > >& OwnerTable)
{
    return SNew(STableRow<TSharedPtr<FString> >, OwnerTable)
        [
            SNew(STextBlock)
            .Text(FText::FromString(*Item))
            .ColorAndOpacity(FLinearColor::Red)
        ];
}

const FSlateBrush* SNeoPakResultDialog::GetResultIcon() const
{
    if (FailureCount == 0)
    {
        return FAppStyle::GetBrush("Icons.SuccessWithColor");
    }
    else if (SuccessCount > 0)
    {
        return FAppStyle::GetBrush("Icons.WarningWithColor");
    }
    else
    {
        return FAppStyle::GetBrush("Icons.ErrorWithColor");
    }
}

FSlateColor SNeoPakResultDialog::GetResultColor() const
{
    if (FailureCount == 0)
    {
        return FLinearColor::Green;
    }
    else if (SuccessCount > 0)
    {
        return FLinearColor::Yellow;
    }
    else
    {
        return FLinearColor::Red;
    }
}

FText SNeoPakResultDialog::GetResultText() const
{
    int32 TotalCount = SuccessCount + FailureCount;
    
    if (FailureCount == 0)
    {
        return FText::FromString(FString::Printf(TEXT("All %d operations completed successfully!"), TotalCount));
    }
    else if (SuccessCount > 0)
    {
        return FText::FromString(FString::Printf(TEXT("%d successful, %d failed out of %d total operations."), 
                                                SuccessCount, FailureCount, TotalCount));
    }
    else
    {
        return FText::FromString(FString::Printf(TEXT("All %d operations failed."), TotalCount));
    }
}

FText SNeoPakResultDialog::GetSummaryText() const
{
    return FText::FromString(Summary);
}

#undef LOCTEXT_NAMESPACE
