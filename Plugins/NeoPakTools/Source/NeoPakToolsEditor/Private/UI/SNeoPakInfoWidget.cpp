// Copyright Epic Games, Inc. All Rights Reserved.

#include "UI/SNeoPakInfoWidget.h"
#include "NeoPakToolsEditor.h"
#include "Utils/NeoDataAssetTypeRegistry.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Layout/SSeparator.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Styling/AppStyle.h"

#define LOCTEXT_NAMESPACE "SNeoPakInfoWidget"

void SNeoPakInfoWidget::Construct(const FArguments& InArgs)
{
    PakFilePath = InArgs._PakFilePath;
    bHasCachedTypeInfo = false;

    ChildSlot
    [
        CreateMainContent()
    ];

    RefreshContent();
}

TSharedRef<SWidget> SNeoPakInfoWidget::CreateMainContent()
{
    return SNew(SVerticalBox)
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("PakInfoTitle", "PAK File Information"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SNew(SSeparator)
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("PakFilePathLabel", "File Path:"))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(PakFilePathText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetFileStatusText)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("FileStatusLabel", "Status:"))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(FileStatusText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetFileStatusText)
                .ColorAndOpacity(this, &SNeoPakInfoWidget::GetFileStatusColor)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("DataAssetTypeLabel", "DataAsset Type:"))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(DataAssetTypeText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetDataAssetTypeText)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("TypeDescriptionLabel", "Description:"))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(TypeDescriptionText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetTypeDescriptionText)
                .AutoWrapText(true)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("SkeletonRequirementLabel", "Requires Skeleton:"))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(SkeletonRequirementText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetSkeletonRequirementText)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("ConfigAssetPathLabel", "Config Asset:"))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SNew(STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetConfigAssetPathText)
                .AutoWrapText(true)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("CreationTimeLabel", "Created:"))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SNew(STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetCreationTimeText)
            ]
        ];
}

void SNeoPakInfoWidget::RefreshContent()
{
    UpdateDisplayedInfo();
}

void SNeoPakInfoWidget::SetPakFilePath(const FString& InPakFilePath)
{
    PakFilePath = InPakFilePath;
    // Clear cache when path changes
    bHasCachedTypeInfo = false;
    RefreshContent();
}

void SNeoPakInfoWidget::UpdateDisplayedInfo()
{
    // Force refresh of all text blocks by invalidating them
    if (PakFilePathText.IsValid())
    {
        PakFilePathText->Invalidate(EInvalidateWidget::Layout);
    }
    if (FileStatusText.IsValid())
    {
        FileStatusText->Invalidate(EInvalidateWidget::Layout);
    }
    if (DataAssetTypeText.IsValid())
    {
        DataAssetTypeText->Invalidate(EInvalidateWidget::Layout);
    }
    if (TypeDescriptionText.IsValid())
    {
        TypeDescriptionText->Invalidate(EInvalidateWidget::Layout);
    }
    if (SkeletonRequirementText.IsValid())
    {
        SkeletonRequirementText->Invalidate(EInvalidateWidget::Layout);
    }
}

FText SNeoPakInfoWidget::GetFileStatusText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoFileSelected", "No file selected");
    }

    bool bFileExists = FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath);
    if (bFileExists)
    {
        return LOCTEXT("FileExists", "File exists");
    }
    else
    {
        return LOCTEXT("FileNotFound", "File not found");
    }
}

FSlateColor SNeoPakInfoWidget::GetFileStatusColor() const
{
    if (PakFilePath.IsEmpty())
    {
        return FSlateColor::UseForeground();
    }

    bool bFileExists = FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath);
    if (bFileExists)
    {
        return FSlateColor(FLinearColor::Green);
    }
    else
    {
        return FSlateColor(FLinearColor::Red);
    }
}

FText SNeoPakInfoWidget::GetDataAssetTypeText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoTypeInfo", "N/A");
    }

    // Use const_cast to update cache (this is safe as we're only caching data)
    const_cast<SNeoPakInfoWidget*>(this)->UpdateCachedTypeInfo();

    if (bHasCachedTypeInfo)
    {
        return FText::FromString(CachedTypeInfo.TypeName);
    }
    else
    {
        return LOCTEXT("NoTypeInfoAvailable", "No type information available");
    }
}

FText SNeoPakInfoWidget::GetTypeDescriptionText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoDescription", "N/A");
    }

    // Use const_cast to update cache (this is safe as we're only caching data)
    const_cast<SNeoPakInfoWidget*>(this)->UpdateCachedTypeInfo();

    if (bHasCachedTypeInfo)
    {
        return FText::FromString(CachedTypeInfo.Description);
    }
    else
    {
        return LOCTEXT("NoDescriptionAvailable", "No description available");
    }
}

FText SNeoPakInfoWidget::GetSkeletonRequirementText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoRequirementInfo", "N/A");
    }

    // Use const_cast to update cache (this is safe as we're only caching data)
    const_cast<SNeoPakInfoWidget*>(this)->UpdateCachedTypeInfo();

    if (bHasCachedTypeInfo)
    {
        return CachedTypeInfo.bRequiresSkeleton ? LOCTEXT("RequiresSkeleton", "Yes") : LOCTEXT("NoSkeletonRequired", "No");
    }
    else
    {
        return LOCTEXT("NoRequirementInfoAvailable", "Unknown");
    }
}

void SNeoPakInfoWidget::UpdateCachedTypeInfo()
{
    // Only update cache if path has changed or cache is invalid
    if (!bHasCachedTypeInfo || CachedPakFilePath != PakFilePath)
    {
        CachedPakFilePath = PakFilePath;

        if (!PakFilePath.IsEmpty() && FNeoDataAssetTypeRegistry::HasDataAssetTypeInfo(PakFilePath))
        {
            CachedTypeInfo = FNeoDataAssetTypeRegistry::GetDataAssetTypeFromPak(PakFilePath);
            bHasCachedTypeInfo = true;
        }
        else
        {
            // Clear cached info if no type info available
            CachedTypeInfo = FNeoDataAssetTypeInfo();
            bHasCachedTypeInfo = false;
        }
    }
}

FText SNeoPakInfoWidget::GetConfigAssetPathText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoConfigAssetPath", "N/A");
    }

    // Use const_cast to update cache (this is safe as we're only caching data)
    const_cast<SNeoPakInfoWidget*>(this)->UpdateCachedTypeInfo();

    if (bHasCachedTypeInfo && !CachedTypeInfo.ConfigAssetPath.IsEmpty())
    {
        return FText::FromString(CachedTypeInfo.ConfigAssetPath);
    }
    else
    {
        return LOCTEXT("NoConfigAssetPathAvailable", "Not available");
    }
}

FText SNeoPakInfoWidget::GetCreationTimeText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoCreationTime", "N/A");
    }

    // Use const_cast to update cache (this is safe as we're only caching data)
    const_cast<SNeoPakInfoWidget*>(this)->UpdateCachedTypeInfo();

    if (bHasCachedTypeInfo && !CachedTypeInfo.CreationTime.IsEmpty())
    {
        return FText::FromString(CachedTypeInfo.CreationTime);
    }
    else
    {
        return LOCTEXT("NoCreationTimeAvailable", "Not available");
    }
}

#undef LOCTEXT_NAMESPACE
