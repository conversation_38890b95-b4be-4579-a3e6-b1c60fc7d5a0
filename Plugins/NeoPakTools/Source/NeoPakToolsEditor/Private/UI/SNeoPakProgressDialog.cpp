#include "UI/SNeoPakProgressDialog.h"
#include "NeoPakToolsEditor.h"
#include "Widgets/SWindow.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Notifications/SProgressBar.h"
#include "Framework/Application/SlateApplication.h"
#include "Styling/AppStyle.h"

#define LOCTEXT_NAMESPACE "SNeoPakProgressDialog"

void SNeoPakProgressDialog::Construct(const FArguments& InArgs)
{
    PakManager = InArgs._PakManager;
    OperationName = InArgs._OperationName;
    CurrentStep = 0;
    TotalSteps = 1;
    CurrentOperationText = TEXT("Initializing...");
    bDelegatesBound = false;

    ChildSlot
    [
        SNew(SBorder)
        .BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
        .Padding(10.0f)
        [
            SNew(SVerticalBox)

            // 操作标题
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(0.0f, 0.0f, 0.0f, 10.0f)
            [
                SNew(STextBlock)
                .Text(FText::FromString(OperationName))
                .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
                .Justification(ETextJustify::Center)
            ]

            // 进度条
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(0.0f, 0.0f, 0.0f, 5.0f)
            [
                SNew(SBox)
                .HeightOverride(20.0f)
                [
                    SAssignNew(ProgressBar, SProgressBar)
                    .Percent(this, &SNeoPakProgressDialog::GetProgressPercent)
                ]
            ]

            // 进度文本
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(0.0f, 0.0f, 0.0f, 5.0f)
            [
                SAssignNew(ProgressText, STextBlock)
                .Text(this, &SNeoPakProgressDialog::GetProgressText)
                .Justification(ETextJustify::Center)
            ]

            // 当前操作描述
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(0.0f, 0.0f, 0.0f, 15.0f)
            [
                SAssignNew(OperationText, STextBlock)
                .Text(this, &SNeoPakProgressDialog::GetOperationText)
                .Justification(ETextJustify::Center)
                .AutoWrapText(true)
            ]

            // 取消按钮
            + SVerticalBox::Slot()
            .AutoHeight()
            [
                SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)

                + SHorizontalBox::Slot()
                .AutoWidth()
                [
                    SAssignNew(CancelButton, SButton)
                    .Text(LOCTEXT("CancelButton", "Cancel"))
                    .OnClicked(this, &SNeoPakProgressDialog::OnCancelClicked)
                    .HAlign(HAlign_Center)
                    .VAlign(VAlign_Center)
                ]

                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
            ]
        ]
    ];

    // 绑定委托
    if (PakManager.IsValid())
    {
        PakManager->OnProgressUpdated.AddSP(this, &SNeoPakProgressDialog::OnProgressUpdated);
        PakManager->OnOperationCancelled.AddSP(this, &SNeoPakProgressDialog::OnOperationCancelled);
        bDelegatesBound = true;
    }

    // 注册定时器检查操作状态
    RegisterActiveTimer(0.1f, FWidgetActiveTimerDelegate::CreateSP(this, &SNeoPakProgressDialog::CheckOperationStatus));
}

TSharedPtr<SWindow> SNeoPakProgressDialog::ShowProgressDialog(TWeakObjectPtr<UNeoPakManager> PakManager, const FString& OperationName)
{
    TSharedRef<SNeoPakProgressDialog> ProgressDialog = SNew(SNeoPakProgressDialog)
        .PakManager(PakManager)
        .OperationName(OperationName);

    TSharedPtr<SWindow> Window = SNew(SWindow)
        .Title(FText::FromString(FString::Printf(TEXT("NeoPakTools - %s"), *OperationName)))
        .SizingRule(ESizingRule::FixedSize)
        .ClientSize(FVector2D(400, 200))
        .SupportsMaximize(false)
        .SupportsMinimize(false)
        .IsTopmostWindow(true)
        [
            ProgressDialog
        ];

    ProgressDialog->DialogWindow = Window;

    FSlateApplication::Get().AddModalWindow(Window.ToSharedRef(), FSlateApplication::Get().GetActiveTopLevelWindow());

    return Window;
}

void SNeoPakProgressDialog::CloseDialog()
{
    // 解绑委托
    if (bDelegatesBound && PakManager.IsValid())
    {
        PakManager->OnProgressUpdated.RemoveAll(this);
        PakManager->OnOperationCancelled.RemoveAll(this);
        bDelegatesBound = false;
    }

    // 关闭窗口
    if (DialogWindow.IsValid())
    {
        DialogWindow.Pin()->RequestDestroyWindow();
    }
}

void SNeoPakProgressDialog::OnProgressUpdated(int32 InCurrentStep, int32 InTotalSteps, const FString& InCurrentOperation)
{
    CurrentStep = InCurrentStep;
    TotalSteps = InTotalSteps;
    CurrentOperationText = InCurrentOperation;

    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Progress Dialog Update: %d/%d - %s"), CurrentStep, TotalSteps, *CurrentOperationText);
}

void SNeoPakProgressDialog::OnOperationCancelled()
{
    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Operation cancelled, closing progress dialog"));
    CloseDialog();
}

FReply SNeoPakProgressDialog::OnCancelClicked()
{
    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("User clicked cancel button"));
    
    if (PakManager.IsValid())
    {
        PakManager->CancelCurrentOperation();
    }

    return FReply::Handled();
}

TOptional<float> SNeoPakProgressDialog::GetProgressPercent() const
{
    if (TotalSteps <= 0)
    {
        return 0.0f;
    }

    return (float)CurrentStep / (float)TotalSteps;
}

FText SNeoPakProgressDialog::GetProgressText() const
{
    return FText::FromString(FString::Printf(TEXT("%d / %d"), CurrentStep, TotalSteps));
}

FText SNeoPakProgressDialog::GetOperationText() const
{
    return FText::FromString(CurrentOperationText);
}

EActiveTimerReturnType SNeoPakProgressDialog::CheckOperationStatus(double InCurrentTime, float InDeltaTime)
{
    // 检查操作是否完成
    if (!PakManager.IsValid() || !PakManager->IsOperationInProgress())
    {
        // 操作完成，直接关闭对话框
        CloseDialog();
        return EActiveTimerReturnType::Stop;
    }

    return EActiveTimerReturnType::Continue;
}

#undef LOCTEXT_NAMESPACE
