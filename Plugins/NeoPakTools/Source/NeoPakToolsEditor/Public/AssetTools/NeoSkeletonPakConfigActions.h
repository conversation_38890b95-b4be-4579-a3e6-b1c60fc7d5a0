// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "AssetTools/NeoPakConfigActionsBase.h"
#include "Config/NeoSkeletonPakConfig.h"

class FNeoSkeletonPakConfigActions : public FNeoPakConfigActionsBase
{
public:
    // IAssetTypeActions Implementation
    virtual UClass* GetSupportedClass() const override;

protected:
    // FNeoPakConfigActionsBase Implementation
    virtual FColor GetAssetTypeColor() const override;
    virtual FText GetAssetTypeName() const override;
};
