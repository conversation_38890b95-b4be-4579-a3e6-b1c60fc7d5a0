// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNeoPakToolsEditor, Log, All);

class FNeoPakToolsEditorModule : public IModuleInterface
{
public:

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

private:
	void RegisterAssetTypeActions();
	void UnregisterAssetTypeActions();
	void RegisterMenuExtensions();
	void UnregisterMenuExtensions();

	TArray<TSharedRef<class IAssetTypeActions>> CreatedAssetTypeActions;
};
