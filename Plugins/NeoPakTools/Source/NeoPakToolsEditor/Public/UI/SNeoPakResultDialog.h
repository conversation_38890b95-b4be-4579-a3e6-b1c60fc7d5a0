#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Views/STableViewBase.h"
#include "Framework/Application/SlateApplication.h"

// 前向声明
class SWindow;
class STextBlock;
class SButton;

/**
 * 操作结果摘要对话框
 */
class NEOPAKTOOLSEDITOR_API SNeoPakResultDialog : public SCompoundWidget
{
public:
    // 类型定义简化模板声明
    using FStringPtr = TSharedPtr<FString>;
    SLATE_BEGIN_ARGS(SNeoPakResultDialog)
        : _Title(TEXT("Operation Result"))
        , _SuccessCount(0)
        , _FailureCount(0)
        {}
        
        SLATE_ARGUMENT(FString, Title)
        SLATE_ARGUMENT(int32, SuccessCount)
        SLATE_ARGUMENT(int32, FailureCount)
        SLATE_ARGUMENT(TArray<FString>, FailedItems)
        SLATE_ARGUMENT(FString, Summary)
    SLATE_END_ARGS()

    /** Constructs this widget with InArgs */
    void Construct(const FArguments& InArgs);

    /** 显示结果对话框 */
    static void ShowResultDialog(
        const FString& Title,
        int32 SuccessCount,
        int32 FailureCount,
        const TArray<FString>& FailedItems,
        const FString& Summary = TEXT("")
    );

private:
    /** 确定按钮点击 */
    FReply OnOKClicked();

    /** 生成失败项目行 */
    TSharedRef<IStringTableRow> OnGenerateFailedItemRow(FStringPtr Item, const TSharedRef<SStringTableViewBase>& OwnerTable);

    /** 获取结果图标 */
    const FSlateBrush* GetResultIcon() const;

    /** 获取结果颜色 */
    FSlateColor GetResultColor() const;

    /** 获取结果文本 */
    FText GetResultText() const;

    /** 获取摘要文本 */
    FText GetSummaryText() const;

private:
    /** 操作标题 */
    FString Title;

    /** 成功数量 */
    int32 SuccessCount;

    /** 失败数量 */
    int32 FailureCount;

    /** 失败项目列表 */
    TArray<FStringPtr> FailedItems;

    /** 摘要信息 */
    FString Summary;

    /** 对话框窗口 */
    TWeakPtr<SWindow> DialogWindow;

    /** 失败项目列表视图 */
    TSharedPtr<SStringListView> FailedItemsListView;
};
