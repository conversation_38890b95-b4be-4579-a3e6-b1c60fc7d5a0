#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Framework/Application/SlateApplication.h"
#include "PakManager/NeoPakManager.h"

// 前向声明
class SWindow;
class STextBlock;
class SButton;
class SProgressBar;

/**
 * 进度显示对话框
 */
class NEOPAKTOOLSEDITOR_API SNeoPakProgressDialog : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SNeoPakProgressDialog)
        : _PakManager(nullptr)
        , _OperationName(TEXT("Operation"))
        {}
        
        SLATE_ARGUMENT(TWeakObjectPtr<UNeoPakManager>, PakManager)
        SLATE_ARGUMENT(FString, OperationName)
    SLATE_END_ARGS()

    /** Constructs this widget with InArgs */
    void Construct(const FArguments& InArgs);

    /** 显示进度对话框 */
    static TSharedPtr<SWindow> ShowProgressDialog(TWeakObjectPtr<UNeoPakManager> PakManager, const FString& OperationName);

    /** 关闭对话框 */
    void CloseDialog();

private:
    /** 进度更新回调 */
    void OnProgressUpdated(int32 CurrentStep, int32 TotalSteps, const FString& CurrentOperation);

    /** 操作取消回调 */
    void OnOperationCancelled();

    /** 取消按钮点击 */
    FReply OnCancelClicked();

    /** 获取进度百分比 */
    TOptional<float> GetProgressPercent() const;

    /** 获取进度文本 */
    FText GetProgressText() const;

    /** 获取操作描述文本 */
    FText GetOperationText() const;

    /** 检查操作是否完成 */
    EActiveTimerReturnType CheckOperationStatus(double InCurrentTime, float InDeltaTime);

private:
    /** PAK管理器引用 */
    TWeakObjectPtr<UNeoPakManager> PakManager;

    /** 操作名称 */
    FString OperationName;

    /** 当前进度 */
    int32 CurrentStep;
    int32 TotalSteps;
    FString CurrentOperationText;

    /** 对话框窗口 */
    TWeakPtr<SWindow> DialogWindow;

    /** 进度条 */
    TSharedPtr<class SProgressBar> ProgressBar;

    /** 进度文本 */
    TSharedPtr<STextBlock> ProgressText;

    /** 操作描述文本 */
    TSharedPtr<STextBlock> OperationText;

    /** 取消按钮 */
    TSharedPtr<SButton> CancelButton;

    /** 是否已绑定委托 */
    bool bDelegatesBound;
};
