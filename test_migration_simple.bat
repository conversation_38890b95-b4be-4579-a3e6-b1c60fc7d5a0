@echo off
echo Testing CustomCook Migration to NeoPakTools Plugin
echo.

echo Checking original files removed...
if exist "Source\NeoLive\CustomCookTool.h" (
    echo ERROR: CustomCookTool.h still exists
    exit /b 1
) else (
    echo OK: CustomCookTool.h removed
)

if exist "Source\NeoLive\CustomCookTool.cpp" (
    echo ERROR: CustomCookTool.cpp still exists
    exit /b 1
) else (
    echo OK: CustomCookTool.cpp removed
)

echo.
echo Checking new plugin files...

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Public\Utils\NeoCustomCookTool.h" (
    echo OK: NeoCustomCookTool.h exists
) else (
    echo ERROR: NeoCustomCookTool.h missing
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Private\Utils\NeoCustomCookTool.cpp" (
    echo OK: NeoCustomCookTool.cpp exists
) else (
    echo ERROR: NeoCustomCookTool.cpp missing
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Public\Utils\NeoCookPakIntegration.h" (
    echo OK: NeoCookPakIntegration.h exists
) else (
    echo ERROR: NeoCookPakIntegration.h missing
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Private\Utils\NeoCookPakIntegration.cpp" (
    echo OK: NeoCookPakIntegration.cpp exists
) else (
    echo ERROR: NeoCookPakIntegration.cpp missing
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Public\Utils\NeoCookPakBlueprintLibrary.h" (
    echo OK: NeoCookPakBlueprintLibrary.h exists
) else (
    echo ERROR: NeoCookPakBlueprintLibrary.h missing
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Private\Utils\NeoCookPakBlueprintLibrary.cpp" (
    echo OK: NeoCookPakBlueprintLibrary.cpp exists
) else (
    echo ERROR: NeoCookPakBlueprintLibrary.cpp missing
    exit /b 1
)

echo.
echo Checking compilation...
if exist "Plugins\NeoPakTools\Binaries\Win64\UnrealEditor-NeoPakTools.dll" (
    echo OK: Plugin DLL exists
) else (
    echo ERROR: Plugin DLL missing
    exit /b 1
)

echo.
echo SUCCESS: CustomCook migration completed successfully!
echo All files moved to NeoPakTools plugin and compiled.
echo.
echo New classes available:
echo - UNeoCustomCookTool
echo - UNeoCookPakIntegration  
echo - UNeoCookPakBlueprintLibrary
echo.
pause
