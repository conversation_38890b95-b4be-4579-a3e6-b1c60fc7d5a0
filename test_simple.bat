@echo off
echo Testing Cook+PAK workflow...

set PROJECT_FILE=E:\UnrealProjects\NeoLive\NeoLive.uproject
set UNREAL_CMD=E:\UnrealEngine\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cmd.exe

echo Checking files...
if not exist "%PROJECT_FILE%" (
    echo Project file not found
    exit /b 1
)

if not exist "%UNREAL_CMD%" (
    echo UnrealEditor-Cmd not found
    exit /b 1
)

echo Files found, starting Cook...
"%UNREAL_CMD%" "%PROJECT_FILE%" -run=Cook -TargetPlatform=Windows -unversioned -stdout -unattended

echo Cook completed with code %ERRORLEVEL%
pause
