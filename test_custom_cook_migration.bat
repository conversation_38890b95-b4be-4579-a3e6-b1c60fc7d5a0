@echo off
echo === Testing CustomCook Migration to NeoPakTools Plugin ===
echo.

echo Checking if original CustomCook files were removed...
if exist "Source\NeoLive\CustomCookTool.h" (
    echo ERROR: Original CustomCookTool.h still exists!
    exit /b 1
) else (
    echo ✅ CustomCookTool.h successfully removed
)

if exist "Source\NeoLive\CustomCookTool.cpp" (
    echo ERROR: Original CustomCookTool.cpp still exists!
    exit /b 1
) else (
    echo ✅ CustomCookTool.cpp successfully removed
)

if exist "Source\NeoLive\CustomCookToolBlueprintLibrary.h" (
    echo ERROR: Original CustomCookToolBlueprintLibrary.h still exists!
    exit /b 1
) else (
    echo ✅ CustomCookToolBlueprintLibrary.h successfully removed
)

if exist "Source\NeoLive\CustomCookToolBlueprintLibrary.cpp" (
    echo ERROR: Original CustomCookToolBlueprintLibrary.cpp still exists!
    exit /b 1
) else (
    echo ✅ CustomCookToolBlueprintLibrary.cpp successfully removed
)

echo.
echo Checking if new plugin files exist...

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Public\Utils\NeoCustomCookTool.h" (
    echo ✅ NeoCustomCookTool.h exists in plugin
) else (
    echo ERROR: NeoCustomCookTool.h not found in plugin!
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Private\Utils\NeoCustomCookTool.cpp" (
    echo ✅ NeoCustomCookTool.cpp exists in plugin
) else (
    echo ERROR: NeoCustomCookTool.cpp not found in plugin!
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Public\Utils\NeoCookPakIntegration.h" (
    echo ✅ NeoCookPakIntegration.h exists in plugin
) else (
    echo ERROR: NeoCookPakIntegration.h not found in plugin!
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Private\Utils\NeoCookPakIntegration.cpp" (
    echo ✅ NeoCookPakIntegration.cpp exists in plugin
) else (
    echo ERROR: NeoCookPakIntegration.cpp not found in plugin!
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Public\Utils\NeoCookPakBlueprintLibrary.h" (
    echo ✅ NeoCookPakBlueprintLibrary.h exists in plugin
) else (
    echo ERROR: NeoCookPakBlueprintLibrary.h not found in plugin!
    exit /b 1
)

if exist "Plugins\NeoPakTools\Source\NeoPakTools\Private\Utils\NeoCookPakBlueprintLibrary.cpp" (
    echo ✅ NeoCookPakBlueprintLibrary.cpp exists in plugin
) else (
    echo ERROR: NeoCookPakBlueprintLibrary.cpp not found in plugin!
    exit /b 1
)

echo.
echo Checking compilation status...
if exist "Plugins\NeoPakTools\Binaries\Win64\UnrealEditor-NeoPakTools.dll" (
    echo ✅ Plugin compiled successfully - DLL exists
) else (
    echo ERROR: Plugin DLL not found - compilation may have failed!
    exit /b 1
)

echo.
echo === CustomCook Migration Test Results ===
echo ✅ All original CustomCook files successfully removed
echo ✅ All new plugin files successfully created
echo ✅ Plugin compiled successfully
echo ✅ Migration completed successfully!
echo.
echo The CustomCook functionality has been successfully moved to the NeoPakTools plugin.
echo You can now use the following new classes:
echo   - UNeoCustomCookTool: Advanced cooking functionality
echo   - UNeoCookPakIntegration: Unified Cook+PAK workflow
echo   - UNeoCookPakBlueprintLibrary: Blueprint interfaces
echo.
pause
