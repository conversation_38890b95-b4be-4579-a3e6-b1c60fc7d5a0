// Runtime PAK测试示例
// 这个文件展示了如何在C++和Blueprint中使用NeoPakTools的runtime功能

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PakManager/NeoPakManager.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Runtime_PAK_Test_Example.generated.h"

UCLASS(BlueprintType, Blueprintable)
class NEOLIVE_API ARuntimePakTestActor : public AActor
{
    GENERATED_BODY()

public:
    ARuntimePakTestActor();

protected:
    virtual void BeginPlay() override;

public:
    // Blueprint可调用的测试函数
    
    UFUNCTION(BlueprintCallable, Category = "PAK Test")
    void TestBasicPakLoading();
    
    UFUNCTION(BlueprintCallable, Category = "PAK Test")
    void TestPakUnloading();
    
    UFUNCTION(BlueprintCallable, Category = "PAK Test")
    void TestAssetLoadingFromPak();
    
    UFUNCTION(BlueprintCallable, Category = "PAK Test")
    void TestPakStatusQueries();
    
    UFUNCTION(BlueprintCallable, Category = "PAK Test")
    bool LoadDLCContent(const FString& DLCName);
    
    UFUNCTION(BlueprintCallable, Category = "PAK Test")
    bool LoadMapFromPak(const FString& MapName);

private:
    UPROPERTY()
    UNeoPakManager* PakManager;
    
    // 测试用的PAK文件路径
    FString TestPakPath;
};

// 实现文件内容

ARuntimePakTestActor::ARuntimePakTestActor()
{
    PrimaryActorTick.bCanEverTick = false;
    
    // 设置测试PAK路径
    TestPakPath = FPaths::ProjectDir() / TEXT("Paks/TestMaps/TestMap1.pak");
}

void ARuntimePakTestActor::BeginPlay()
{
    Super::BeginPlay();
    
    // 获取PAK管理器实例
    PakManager = UNeoPakManager::GetInstance();
    
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get PAK Manager instance"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("Runtime PAK Test Actor initialized"));
}

void ARuntimePakTestActor::TestBasicPakLoading()
{
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("PAK Manager not available"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("=== Testing Basic PAK Loading ==="));
    
    // 验证PAK文件完整性
    if (!PakManager->VerifyPakIntegrity(TestPakPath))
    {
        UE_LOG(LogTemp, Error, TEXT("PAK integrity check failed: %s"), *TestPakPath);
        return;
    }
    
    // 加载PAK文件
    bool bSuccess = PakManager->LoadPakFile(TestPakPath);
    
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("✓ PAK loaded successfully: %s"), *TestPakPath);
        
        // 检查加载状态
        bool bIsLoaded = PakManager->IsPakFileLoaded(TestPakPath);
        UE_LOG(LogTemp, Log, TEXT("✓ PAK load status verified: %s"), bIsLoaded ? TEXT("Loaded") : TEXT("Not Loaded"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("✗ Failed to load PAK: %s"), *TestPakPath);
    }
}

void ARuntimePakTestActor::TestPakUnloading()
{
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("PAK Manager not available"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("=== Testing PAK Unloading ==="));
    
    // 检查PAK是否已加载
    if (!PakManager->IsPakFileLoaded(TestPakPath))
    {
        UE_LOG(LogTemp, Warning, TEXT("PAK is not loaded, loading first: %s"), *TestPakPath);
        PakManager->LoadPakFile(TestPakPath);
    }
    
    // 卸载PAK文件
    bool bSuccess = PakManager->UnloadPakFile(TestPakPath);
    
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("✓ PAK unloaded successfully: %s"), *TestPakPath);
        
        // 验证卸载状态
        bool bIsLoaded = PakManager->IsPakFileLoaded(TestPakPath);
        UE_LOG(LogTemp, Log, TEXT("✓ PAK unload status verified: %s"), bIsLoaded ? TEXT("Still Loaded") : TEXT("Unloaded"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("✗ Failed to unload PAK: %s"), *TestPakPath);
    }
}

void ARuntimePakTestActor::TestAssetLoadingFromPak()
{
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("PAK Manager not available"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("=== Testing Asset Loading from PAK ==="));
    
    // 确保PAK已加载
    if (!PakManager->IsPakFileLoaded(TestPakPath))
    {
        UE_LOG(LogTemp, Log, TEXT("Loading PAK first: %s"), *TestPakPath);
        if (!PakManager->LoadPakFile(TestPakPath))
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to load PAK for asset testing"));
            return;
        }
    }
    
    // 测试加载不同类型的资产
    TArray<FString> TestAssetPaths = {
        TEXT("/Game/Maps/TestMap1/NewMap"),
        TEXT("/Game/Maps/TestMap1/NewDataAsset"),
        TEXT("/Game/Maps/TestMap1/NewBlueprint"),
        TEXT("/Game/Maps/TestMap1/NewBlueprint1")
    };
    
    for (const FString& AssetPath : TestAssetPaths)
    {
        UObject* LoadedAsset = nullptr;
        bool bAssetLoaded = PakManager->LoadAssetFromPak(AssetPath, LoadedAsset);
        
        if (bAssetLoaded && LoadedAsset)
        {
            UE_LOG(LogTemp, Log, TEXT("✓ Successfully loaded asset: %s (Class: %s)"), 
                *AssetPath, *LoadedAsset->GetClass()->GetName());
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("✗ Failed to load asset: %s"), *AssetPath);
        }
    }
}

void ARuntimePakTestActor::TestPakStatusQueries()
{
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("PAK Manager not available"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("=== Testing PAK Status Queries ==="));
    
    // 获取所有已加载的PAK文件
    TArray<FString> LoadedPaks = PakManager->GetLoadedPakFiles();
    
    UE_LOG(LogTemp, Log, TEXT("Currently loaded PAK files: %d"), LoadedPaks.Num());
    for (const FString& PakPath : LoadedPaks)
    {
        UE_LOG(LogTemp, Log, TEXT("  - %s"), *PakPath);
        
        // 验证每个PAK的完整性
        bool bIsValid = PakManager->VerifyPakIntegrity(PakPath);
        UE_LOG(LogTemp, Log, TEXT("    Integrity: %s"), bIsValid ? TEXT("Valid") : TEXT("Invalid"));
    }
}

bool ARuntimePakTestActor::LoadDLCContent(const FString& DLCName)
{
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("PAK Manager not available"));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("=== Loading DLC Content: %s ==="), *DLCName);
    
    // 构建DLC PAK路径
    FString DLCPakPath = FPaths::ProjectDir() / TEXT("DLC") / (DLCName + TEXT(".pak"));
    
    // 验证DLC PAK完整性
    if (!PakManager->VerifyPakIntegrity(DLCPakPath))
    {
        UE_LOG(LogTemp, Error, TEXT("DLC PAK integrity check failed: %s"), *DLCName);
        return false;
    }
    
    // 加载DLC PAK
    if (!PakManager->LoadPakFile(DLCPakPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load DLC PAK: %s"), *DLCName);
        return false;
    }
    
    // 尝试加载DLC主资产
    FString DLCAssetPath = FString::Printf(TEXT("/Game/DLC/%s/MainAsset"), *DLCName);
    UObject* DLCAsset = nullptr;
    
    if (PakManager->LoadAssetFromPak(DLCAssetPath, DLCAsset))
    {
        UE_LOG(LogTemp, Log, TEXT("✓ DLC content loaded successfully: %s"), *DLCName);
        return true;
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("DLC PAK loaded but main asset not found: %s"), *DLCAssetPath);
        return false;
    }
}

bool ARuntimePakTestActor::LoadMapFromPak(const FString& MapName)
{
    if (!PakManager)
    {
        UE_LOG(LogTemp, Error, TEXT("PAK Manager not available"));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("=== Loading Map from PAK: %s ==="), *MapName);
    
    // 构建地图PAK路径
    FString MapPakPath = FPaths::ProjectDir() / TEXT("Paks/Maps") / (MapName + TEXT(".pak"));
    
    // 加载地图PAK
    if (!PakManager->LoadPakFile(MapPakPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load map PAK: %s"), *MapName);
        return false;
    }
    
    // 加载地图资产
    FString MapAssetPath = FString::Printf(TEXT("/Game/Maps/%s/%s"), *MapName, *MapName);
    UObject* MapAsset = nullptr;
    
    if (PakManager->LoadAssetFromPak(MapAssetPath, MapAsset))
    {
        UWorld* MapWorld = Cast<UWorld>(MapAsset);
        if (MapWorld)
        {
            UE_LOG(LogTemp, Log, TEXT("✓ Map loaded successfully, switching to: %s"), *MapName);
            
            // 切换到新地图
            UGameplayStatics::OpenLevel(this, FName(*MapName));
            return true;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Loaded asset is not a valid world: %s"), *MapAssetPath);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load map asset: %s"), *MapAssetPath);
    }
    
    return false;
}

/*
Blueprint使用示例：

1. 创建一个Blueprint基于ARuntimePakTestActor
2. 在Event BeginPlay中调用测试函数：
   - Call TestBasicPakLoading
   - Call TestAssetLoadingFromPak
   - Call TestPakStatusQueries

3. 创建UI按钮来触发不同的测试：
   - Button "Load PAK" -> Call TestBasicPakLoading
   - Button "Unload PAK" -> Call TestPakUnloading
   - Button "Load DLC" -> Call LoadDLCContent with DLC name
   - Button "Load Map" -> Call LoadMapFromPak with map name

4. 监控日志输出来查看测试结果
*/
